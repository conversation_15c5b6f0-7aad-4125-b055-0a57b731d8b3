<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:tns="http://tempuri.org/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="CreateCreditCardProfile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="profileReferenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firstName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="lastName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="companyName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="address1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="address2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="city" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="zipCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="country" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="phoneNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="email" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ccardNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="cvNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="cardType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="expMonth" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="expYear" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="currency" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateCreditCardProfileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateCreditCardProfileResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfString">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="AuthorizeCreditcard">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="referenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="suscriptionID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AuthorizeCreditcardResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="AuthorizeCreditcardResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ReverseAuthorization">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="referenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="authorizationRequestID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="currency" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ReverseAuthorizationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ReverseAuthorizationResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateProfileInformation">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="subscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firstName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="lastName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="companyName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="address1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="address2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="city" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="zipCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="country" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="phoneNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="email" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateProfileInformationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpdateProfileInformationResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateCreditCardInformation">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="subscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ccardNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="cvNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="cardType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="expMonth" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="expYear" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateCreditCardInformationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpdateCreditCardInformationResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelProfile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="subscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="profileReferenceCode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelProfileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CancelProfileResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChargeMoney">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="referenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="suscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="serviceName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="value" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChargeMoneyResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ChargeMoneyResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDebitElectronicCheckProfile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="referenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firstName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="lastName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="companyName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="dateOfBirth" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="address1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="address2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="city" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="zipCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="country" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="phoneNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="email" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="accountNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="accountType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="bankTransitNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="currency" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDebitElectronicCheckProfileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateDebitElectronicCheckProfileResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateDebitElectronicCheckProfile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="subscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firstName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="lastName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="companyName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="dateOfBirth" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="address1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="address2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="city" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="zipCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="country" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="phoneNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="email" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="accountNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="accountType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="bankTransitNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="currency" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateDebitElectronicCheckProfileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpdateDebitElectronicCheckProfileResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDebitElectronicCheckTransaction">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="referenceCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="suscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="checkNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="serviceName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="value" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDebitElectronicCheckTransactionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateDebitElectronicCheckTransactionResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="CreateCreditCardProfileSoapIn">
    <wsdl:part name="parameters" element="tns:CreateCreditCardProfile" />
  </wsdl:message>
  <wsdl:message name="CreateCreditCardProfileSoapOut">
    <wsdl:part name="parameters" element="tns:CreateCreditCardProfileResponse" />
  </wsdl:message>
  <wsdl:message name="AuthorizeCreditcardSoapIn">
    <wsdl:part name="parameters" element="tns:AuthorizeCreditcard" />
  </wsdl:message>
  <wsdl:message name="AuthorizeCreditcardSoapOut">
    <wsdl:part name="parameters" element="tns:AuthorizeCreditcardResponse" />
  </wsdl:message>
  <wsdl:message name="ReverseAuthorizationSoapIn">
    <wsdl:part name="parameters" element="tns:ReverseAuthorization" />
  </wsdl:message>
  <wsdl:message name="ReverseAuthorizationSoapOut">
    <wsdl:part name="parameters" element="tns:ReverseAuthorizationResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateProfileInformationSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateProfileInformation" />
  </wsdl:message>
  <wsdl:message name="UpdateProfileInformationSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateProfileInformationResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateCreditCardInformationSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateCreditCardInformation" />
  </wsdl:message>
  <wsdl:message name="UpdateCreditCardInformationSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateCreditCardInformationResponse" />
  </wsdl:message>
  <wsdl:message name="CancelProfileSoapIn">
    <wsdl:part name="parameters" element="tns:CancelProfile" />
  </wsdl:message>
  <wsdl:message name="CancelProfileSoapOut">
    <wsdl:part name="parameters" element="tns:CancelProfileResponse" />
  </wsdl:message>
  <wsdl:message name="ChargeMoneySoapIn">
    <wsdl:part name="parameters" element="tns:ChargeMoney" />
  </wsdl:message>
  <wsdl:message name="ChargeMoneySoapOut">
    <wsdl:part name="parameters" element="tns:ChargeMoneyResponse" />
  </wsdl:message>
  <wsdl:message name="CreateDebitElectronicCheckProfileSoapIn">
    <wsdl:part name="parameters" element="tns:CreateDebitElectronicCheckProfile" />
  </wsdl:message>
  <wsdl:message name="CreateDebitElectronicCheckProfileSoapOut">
    <wsdl:part name="parameters" element="tns:CreateDebitElectronicCheckProfileResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateDebitElectronicCheckProfileSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateDebitElectronicCheckProfile" />
  </wsdl:message>
  <wsdl:message name="UpdateDebitElectronicCheckProfileSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateDebitElectronicCheckProfileResponse" />
  </wsdl:message>
  <wsdl:message name="CreateDebitElectronicCheckTransactionSoapIn">
    <wsdl:part name="parameters" element="tns:CreateDebitElectronicCheckTransaction" />
  </wsdl:message>
  <wsdl:message name="CreateDebitElectronicCheckTransactionSoapOut">
    <wsdl:part name="parameters" element="tns:CreateDebitElectronicCheckTransactionResponse" />
  </wsdl:message>
  <wsdl:portType name="ProcessorSoap">
    <wsdl:operation name="CreateCreditCardProfile">
      <wsdl:input message="tns:CreateCreditCardProfileSoapIn" />
      <wsdl:output message="tns:CreateCreditCardProfileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AuthorizeCreditcard">
      <wsdl:input message="tns:AuthorizeCreditcardSoapIn" />
      <wsdl:output message="tns:AuthorizeCreditcardSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ReverseAuthorization">
      <wsdl:input message="tns:ReverseAuthorizationSoapIn" />
      <wsdl:output message="tns:ReverseAuthorizationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpdateProfileInformation">
      <wsdl:input message="tns:UpdateProfileInformationSoapIn" />
      <wsdl:output message="tns:UpdateProfileInformationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpdateCreditCardInformation">
      <wsdl:input message="tns:UpdateCreditCardInformationSoapIn" />
      <wsdl:output message="tns:UpdateCreditCardInformationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CancelProfile">
      <wsdl:input message="tns:CancelProfileSoapIn" />
      <wsdl:output message="tns:CancelProfileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ChargeMoney">
      <wsdl:input message="tns:ChargeMoneySoapIn" />
      <wsdl:output message="tns:ChargeMoneySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateDebitElectronicCheckProfile">
      <wsdl:input message="tns:CreateDebitElectronicCheckProfileSoapIn" />
      <wsdl:output message="tns:CreateDebitElectronicCheckProfileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpdateDebitElectronicCheckProfile">
      <wsdl:input message="tns:UpdateDebitElectronicCheckProfileSoapIn" />
      <wsdl:output message="tns:UpdateDebitElectronicCheckProfileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateDebitElectronicCheckTransaction">
      <wsdl:input message="tns:CreateDebitElectronicCheckTransactionSoapIn" />
      <wsdl:output message="tns:CreateDebitElectronicCheckTransactionSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ProcessorSoap" type="tns:ProcessorSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CreateCreditCardProfile">
      <soap:operation soapAction="http://tempuri.org/CreateCreditCardProfile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AuthorizeCreditcard">
      <soap:operation soapAction="http://tempuri.org/AuthorizeCreditcard" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReverseAuthorization">
      <soap:operation soapAction="http://tempuri.org/ReverseAuthorization" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateProfileInformation">
      <soap:operation soapAction="http://tempuri.org/UpdateProfileInformation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCreditCardInformation">
      <soap:operation soapAction="http://tempuri.org/UpdateCreditCardInformation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelProfile">
      <soap:operation soapAction="http://tempuri.org/CancelProfile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChargeMoney">
      <soap:operation soapAction="http://tempuri.org/ChargeMoney" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDebitElectronicCheckProfile">
      <soap:operation soapAction="http://tempuri.org/CreateDebitElectronicCheckProfile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateDebitElectronicCheckProfile">
      <soap:operation soapAction="http://tempuri.org/UpdateDebitElectronicCheckProfile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDebitElectronicCheckTransaction">
      <soap:operation soapAction="http://tempuri.org/CreateDebitElectronicCheckTransaction" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Processor">
    <wsdl:port name="ProcessorSoap" binding="tns:ProcessorSoap">
      <soap:address location="http://ccardprocessor/Processor.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>