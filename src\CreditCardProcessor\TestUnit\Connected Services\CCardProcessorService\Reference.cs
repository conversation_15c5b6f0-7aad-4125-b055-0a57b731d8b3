﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TestUnit.CCardProcessorService {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="ArrayOfString", Namespace="http://tempuri.org/", ItemName="string")]
    [System.SerializableAttribute()]
    public class ArrayOfString : System.Collections.Generic.List<string> {
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="CCardProcessorService.ProcessorSoap")]
    public interface ProcessorSoap {
        
        // CODEGEN: Generating message contract since element name profileReferenceCode from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CreateCreditCardProfile", ReplyAction="*")]
        TestUnit.CCardProcessorService.CreateCreditCardProfileResponse CreateCreditCardProfile(TestUnit.CCardProcessorService.CreateCreditCardProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CreateCreditCardProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateCreditCardProfileResponse> CreateCreditCardProfileAsync(TestUnit.CCardProcessorService.CreateCreditCardProfileRequest request);
        
        // CODEGEN: Generating message contract since element name referenceCode from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/AuthorizeCreditcard", ReplyAction="*")]
        TestUnit.CCardProcessorService.AuthorizeCreditcardResponse AuthorizeCreditcard(TestUnit.CCardProcessorService.AuthorizeCreditcardRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/AuthorizeCreditcard", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.AuthorizeCreditcardResponse> AuthorizeCreditcardAsync(TestUnit.CCardProcessorService.AuthorizeCreditcardRequest request);
        
        // CODEGEN: Generating message contract since element name referenceCode from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ReverseAuthorization", ReplyAction="*")]
        TestUnit.CCardProcessorService.ReverseAuthorizationResponse ReverseAuthorization(TestUnit.CCardProcessorService.ReverseAuthorizationRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ReverseAuthorization", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.ReverseAuthorizationResponse> ReverseAuthorizationAsync(TestUnit.CCardProcessorService.ReverseAuthorizationRequest request);
        
        // CODEGEN: Generating message contract since element name subscriptionID from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateProfileInformation", ReplyAction="*")]
        TestUnit.CCardProcessorService.UpdateProfileInformationResponse UpdateProfileInformation(TestUnit.CCardProcessorService.UpdateProfileInformationRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateProfileInformation", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateProfileInformationResponse> UpdateProfileInformationAsync(TestUnit.CCardProcessorService.UpdateProfileInformationRequest request);
        
        // CODEGEN: Generating message contract since element name subscriptionID from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateCreditCardInformation", ReplyAction="*")]
        TestUnit.CCardProcessorService.UpdateCreditCardInformationResponse UpdateCreditCardInformation(TestUnit.CCardProcessorService.UpdateCreditCardInformationRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateCreditCardInformation", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateCreditCardInformationResponse> UpdateCreditCardInformationAsync(TestUnit.CCardProcessorService.UpdateCreditCardInformationRequest request);
        
        // CODEGEN: Generating message contract since element name subscriptionID from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CancelProfile", ReplyAction="*")]
        TestUnit.CCardProcessorService.CancelProfileResponse CancelProfile(TestUnit.CCardProcessorService.CancelProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CancelProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CancelProfileResponse> CancelProfileAsync(TestUnit.CCardProcessorService.CancelProfileRequest request);
        
        // CODEGEN: Generating message contract since element name referenceCode from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ChargeMoney", ReplyAction="*")]
        TestUnit.CCardProcessorService.ChargeMoneyResponse ChargeMoney(TestUnit.CCardProcessorService.ChargeMoneyRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ChargeMoney", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.ChargeMoneyResponse> ChargeMoneyAsync(TestUnit.CCardProcessorService.ChargeMoneyRequest request);
        
        // CODEGEN: Generating message contract since element name referenceCode from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CreateDebitElectronicCheckProfile", ReplyAction="*")]
        TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileResponse CreateDebitElectronicCheckProfile(TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CreateDebitElectronicCheckProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileResponse> CreateDebitElectronicCheckProfileAsync(TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequest request);
        
        // CODEGEN: Generating message contract since element name subscriptionID from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateDebitElectronicCheckProfile", ReplyAction="*")]
        TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileResponse UpdateDebitElectronicCheckProfile(TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateDebitElectronicCheckProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileResponse> UpdateDebitElectronicCheckProfileAsync(TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequest request);
        
        // CODEGEN: Generating message contract since element name referenceCode from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CreateDebitElectronicCheckTransaction", ReplyAction="*")]
        TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionResponse CreateDebitElectronicCheckTransaction(TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CreateDebitElectronicCheckTransaction", ReplyAction="*")]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionResponse> CreateDebitElectronicCheckTransactionAsync(TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CreateCreditCardProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CreateCreditCardProfile", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.CreateCreditCardProfileRequestBody Body;
        
        public CreateCreditCardProfileRequest() {
        }
        
        public CreateCreditCardProfileRequest(TestUnit.CCardProcessorService.CreateCreditCardProfileRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CreateCreditCardProfileRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string profileReferenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string firstName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string lastName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string companyName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string address1;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string address2;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string city;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string state;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string zipCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string country;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string phoneNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public string email;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string ccardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=13)]
        public string cvNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=14)]
        public string cardType;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=15)]
        public string expMonth;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=16)]
        public string expYear;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=17)]
        public string currency;
        
        public CreateCreditCardProfileRequestBody() {
        }
        
        public CreateCreditCardProfileRequestBody(
                    string profileReferenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string ccardNumber, 
                    string cvNumber, 
                    string cardType, 
                    string expMonth, 
                    string expYear, 
                    string currency) {
            this.profileReferenceCode = profileReferenceCode;
            this.firstName = firstName;
            this.lastName = lastName;
            this.companyName = companyName;
            this.address1 = address1;
            this.address2 = address2;
            this.city = city;
            this.state = state;
            this.zipCode = zipCode;
            this.country = country;
            this.phoneNumber = phoneNumber;
            this.email = email;
            this.ccardNumber = ccardNumber;
            this.cvNumber = cvNumber;
            this.cardType = cardType;
            this.expMonth = expMonth;
            this.expYear = expYear;
            this.currency = currency;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CreateCreditCardProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CreateCreditCardProfileResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.CreateCreditCardProfileResponseBody Body;
        
        public CreateCreditCardProfileResponse() {
        }
        
        public CreateCreditCardProfileResponse(TestUnit.CCardProcessorService.CreateCreditCardProfileResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CreateCreditCardProfileResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString CreateCreditCardProfileResult;
        
        public CreateCreditCardProfileResponseBody() {
        }
        
        public CreateCreditCardProfileResponseBody(TestUnit.CCardProcessorService.ArrayOfString CreateCreditCardProfileResult) {
            this.CreateCreditCardProfileResult = CreateCreditCardProfileResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class AuthorizeCreditcardRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="AuthorizeCreditcard", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.AuthorizeCreditcardRequestBody Body;
        
        public AuthorizeCreditcardRequest() {
        }
        
        public AuthorizeCreditcardRequest(TestUnit.CCardProcessorService.AuthorizeCreditcardRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class AuthorizeCreditcardRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string referenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string suscriptionID;
        
        public AuthorizeCreditcardRequestBody() {
        }
        
        public AuthorizeCreditcardRequestBody(string referenceCode, string suscriptionID) {
            this.referenceCode = referenceCode;
            this.suscriptionID = suscriptionID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class AuthorizeCreditcardResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="AuthorizeCreditcardResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.AuthorizeCreditcardResponseBody Body;
        
        public AuthorizeCreditcardResponse() {
        }
        
        public AuthorizeCreditcardResponse(TestUnit.CCardProcessorService.AuthorizeCreditcardResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class AuthorizeCreditcardResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString AuthorizeCreditcardResult;
        
        public AuthorizeCreditcardResponseBody() {
        }
        
        public AuthorizeCreditcardResponseBody(TestUnit.CCardProcessorService.ArrayOfString AuthorizeCreditcardResult) {
            this.AuthorizeCreditcardResult = AuthorizeCreditcardResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ReverseAuthorizationRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="ReverseAuthorization", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.ReverseAuthorizationRequestBody Body;
        
        public ReverseAuthorizationRequest() {
        }
        
        public ReverseAuthorizationRequest(TestUnit.CCardProcessorService.ReverseAuthorizationRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class ReverseAuthorizationRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string referenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string authorizationRequestID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string currency;
        
        public ReverseAuthorizationRequestBody() {
        }
        
        public ReverseAuthorizationRequestBody(string referenceCode, string authorizationRequestID, string currency) {
            this.referenceCode = referenceCode;
            this.authorizationRequestID = authorizationRequestID;
            this.currency = currency;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ReverseAuthorizationResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="ReverseAuthorizationResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.ReverseAuthorizationResponseBody Body;
        
        public ReverseAuthorizationResponse() {
        }
        
        public ReverseAuthorizationResponse(TestUnit.CCardProcessorService.ReverseAuthorizationResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class ReverseAuthorizationResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString ReverseAuthorizationResult;
        
        public ReverseAuthorizationResponseBody() {
        }
        
        public ReverseAuthorizationResponseBody(TestUnit.CCardProcessorService.ArrayOfString ReverseAuthorizationResult) {
            this.ReverseAuthorizationResult = ReverseAuthorizationResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateProfileInformationRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateProfileInformation", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.UpdateProfileInformationRequestBody Body;
        
        public UpdateProfileInformationRequest() {
        }
        
        public UpdateProfileInformationRequest(TestUnit.CCardProcessorService.UpdateProfileInformationRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateProfileInformationRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string subscriptionID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string referenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string firstName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string lastName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string companyName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string address1;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string address2;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string city;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string state;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string zipCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string country;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public string phoneNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string email;
        
        public UpdateProfileInformationRequestBody() {
        }
        
        public UpdateProfileInformationRequestBody(string subscriptionID, string referenceCode, string firstName, string lastName, string companyName, string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email) {
            this.subscriptionID = subscriptionID;
            this.referenceCode = referenceCode;
            this.firstName = firstName;
            this.lastName = lastName;
            this.companyName = companyName;
            this.address1 = address1;
            this.address2 = address2;
            this.city = city;
            this.state = state;
            this.zipCode = zipCode;
            this.country = country;
            this.phoneNumber = phoneNumber;
            this.email = email;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateProfileInformationResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateProfileInformationResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.UpdateProfileInformationResponseBody Body;
        
        public UpdateProfileInformationResponse() {
        }
        
        public UpdateProfileInformationResponse(TestUnit.CCardProcessorService.UpdateProfileInformationResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateProfileInformationResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString UpdateProfileInformationResult;
        
        public UpdateProfileInformationResponseBody() {
        }
        
        public UpdateProfileInformationResponseBody(TestUnit.CCardProcessorService.ArrayOfString UpdateProfileInformationResult) {
            this.UpdateProfileInformationResult = UpdateProfileInformationResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateCreditCardInformationRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateCreditCardInformation", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.UpdateCreditCardInformationRequestBody Body;
        
        public UpdateCreditCardInformationRequest() {
        }
        
        public UpdateCreditCardInformationRequest(TestUnit.CCardProcessorService.UpdateCreditCardInformationRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateCreditCardInformationRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string subscriptionID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string referenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string ccardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string cvNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string cardType;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string expMonth;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string expYear;
        
        public UpdateCreditCardInformationRequestBody() {
        }
        
        public UpdateCreditCardInformationRequestBody(string subscriptionID, string referenceCode, string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear) {
            this.subscriptionID = subscriptionID;
            this.referenceCode = referenceCode;
            this.ccardNumber = ccardNumber;
            this.cvNumber = cvNumber;
            this.cardType = cardType;
            this.expMonth = expMonth;
            this.expYear = expYear;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateCreditCardInformationResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateCreditCardInformationResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.UpdateCreditCardInformationResponseBody Body;
        
        public UpdateCreditCardInformationResponse() {
        }
        
        public UpdateCreditCardInformationResponse(TestUnit.CCardProcessorService.UpdateCreditCardInformationResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateCreditCardInformationResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString UpdateCreditCardInformationResult;
        
        public UpdateCreditCardInformationResponseBody() {
        }
        
        public UpdateCreditCardInformationResponseBody(TestUnit.CCardProcessorService.ArrayOfString UpdateCreditCardInformationResult) {
            this.UpdateCreditCardInformationResult = UpdateCreditCardInformationResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CancelProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CancelProfile", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.CancelProfileRequestBody Body;
        
        public CancelProfileRequest() {
        }
        
        public CancelProfileRequest(TestUnit.CCardProcessorService.CancelProfileRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CancelProfileRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string subscriptionID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string profileReferenceCode;
        
        public CancelProfileRequestBody() {
        }
        
        public CancelProfileRequestBody(string subscriptionID, string profileReferenceCode) {
            this.subscriptionID = subscriptionID;
            this.profileReferenceCode = profileReferenceCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CancelProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CancelProfileResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.CancelProfileResponseBody Body;
        
        public CancelProfileResponse() {
        }
        
        public CancelProfileResponse(TestUnit.CCardProcessorService.CancelProfileResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CancelProfileResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString CancelProfileResult;
        
        public CancelProfileResponseBody() {
        }
        
        public CancelProfileResponseBody(TestUnit.CCardProcessorService.ArrayOfString CancelProfileResult) {
            this.CancelProfileResult = CancelProfileResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ChargeMoneyRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="ChargeMoney", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.ChargeMoneyRequestBody Body;
        
        public ChargeMoneyRequest() {
        }
        
        public ChargeMoneyRequest(TestUnit.CCardProcessorService.ChargeMoneyRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class ChargeMoneyRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string referenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string suscriptionID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string serviceName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string value;
        
        public ChargeMoneyRequestBody() {
        }
        
        public ChargeMoneyRequestBody(string referenceCode, string suscriptionID, string serviceName, string value) {
            this.referenceCode = referenceCode;
            this.suscriptionID = suscriptionID;
            this.serviceName = serviceName;
            this.value = value;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ChargeMoneyResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="ChargeMoneyResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.ChargeMoneyResponseBody Body;
        
        public ChargeMoneyResponse() {
        }
        
        public ChargeMoneyResponse(TestUnit.CCardProcessorService.ChargeMoneyResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class ChargeMoneyResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString ChargeMoneyResult;
        
        public ChargeMoneyResponseBody() {
        }
        
        public ChargeMoneyResponseBody(TestUnit.CCardProcessorService.ArrayOfString ChargeMoneyResult) {
            this.ChargeMoneyResult = ChargeMoneyResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CreateDebitElectronicCheckProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CreateDebitElectronicCheckProfile", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequestBody Body;
        
        public CreateDebitElectronicCheckProfileRequest() {
        }
        
        public CreateDebitElectronicCheckProfileRequest(TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CreateDebitElectronicCheckProfileRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string referenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string firstName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string lastName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string companyName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string dateOfBirth;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string address1;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string address2;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string city;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string state;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string zipCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string country;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public string phoneNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string email;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=13)]
        public string accountNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=14)]
        public string accountType;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=15)]
        public string bankTransitNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=16)]
        public string currency;
        
        public CreateDebitElectronicCheckProfileRequestBody() {
        }
        
        public CreateDebitElectronicCheckProfileRequestBody(
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            this.referenceCode = referenceCode;
            this.firstName = firstName;
            this.lastName = lastName;
            this.companyName = companyName;
            this.dateOfBirth = dateOfBirth;
            this.address1 = address1;
            this.address2 = address2;
            this.city = city;
            this.state = state;
            this.zipCode = zipCode;
            this.country = country;
            this.phoneNumber = phoneNumber;
            this.email = email;
            this.accountNumber = accountNumber;
            this.accountType = accountType;
            this.bankTransitNumber = bankTransitNumber;
            this.currency = currency;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CreateDebitElectronicCheckProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CreateDebitElectronicCheckProfileResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileResponseBody Body;
        
        public CreateDebitElectronicCheckProfileResponse() {
        }
        
        public CreateDebitElectronicCheckProfileResponse(TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CreateDebitElectronicCheckProfileResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString CreateDebitElectronicCheckProfileResult;
        
        public CreateDebitElectronicCheckProfileResponseBody() {
        }
        
        public CreateDebitElectronicCheckProfileResponseBody(TestUnit.CCardProcessorService.ArrayOfString CreateDebitElectronicCheckProfileResult) {
            this.CreateDebitElectronicCheckProfileResult = CreateDebitElectronicCheckProfileResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateDebitElectronicCheckProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateDebitElectronicCheckProfile", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequestBody Body;
        
        public UpdateDebitElectronicCheckProfileRequest() {
        }
        
        public UpdateDebitElectronicCheckProfileRequest(TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateDebitElectronicCheckProfileRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string subscriptionID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string referenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string firstName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string lastName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string companyName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string dateOfBirth;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string address1;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string address2;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string city;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string state;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string zipCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public string country;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string phoneNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=13)]
        public string email;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=14)]
        public string accountNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=15)]
        public string accountType;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=16)]
        public string bankTransitNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=17)]
        public string currency;
        
        public UpdateDebitElectronicCheckProfileRequestBody() {
        }
        
        public UpdateDebitElectronicCheckProfileRequestBody(
                    string subscriptionID, 
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            this.subscriptionID = subscriptionID;
            this.referenceCode = referenceCode;
            this.firstName = firstName;
            this.lastName = lastName;
            this.companyName = companyName;
            this.dateOfBirth = dateOfBirth;
            this.address1 = address1;
            this.address2 = address2;
            this.city = city;
            this.state = state;
            this.zipCode = zipCode;
            this.country = country;
            this.phoneNumber = phoneNumber;
            this.email = email;
            this.accountNumber = accountNumber;
            this.accountType = accountType;
            this.bankTransitNumber = bankTransitNumber;
            this.currency = currency;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateDebitElectronicCheckProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateDebitElectronicCheckProfileResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileResponseBody Body;
        
        public UpdateDebitElectronicCheckProfileResponse() {
        }
        
        public UpdateDebitElectronicCheckProfileResponse(TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateDebitElectronicCheckProfileResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString UpdateDebitElectronicCheckProfileResult;
        
        public UpdateDebitElectronicCheckProfileResponseBody() {
        }
        
        public UpdateDebitElectronicCheckProfileResponseBody(TestUnit.CCardProcessorService.ArrayOfString UpdateDebitElectronicCheckProfileResult) {
            this.UpdateDebitElectronicCheckProfileResult = UpdateDebitElectronicCheckProfileResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CreateDebitElectronicCheckTransactionRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CreateDebitElectronicCheckTransaction", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequestBody Body;
        
        public CreateDebitElectronicCheckTransactionRequest() {
        }
        
        public CreateDebitElectronicCheckTransactionRequest(TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CreateDebitElectronicCheckTransactionRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string referenceCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string suscriptionID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string checkNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string serviceName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string value;
        
        public CreateDebitElectronicCheckTransactionRequestBody() {
        }
        
        public CreateDebitElectronicCheckTransactionRequestBody(string referenceCode, string suscriptionID, string checkNumber, string serviceName, string value) {
            this.referenceCode = referenceCode;
            this.suscriptionID = suscriptionID;
            this.checkNumber = checkNumber;
            this.serviceName = serviceName;
            this.value = value;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CreateDebitElectronicCheckTransactionResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CreateDebitElectronicCheckTransactionResponse", Namespace="http://tempuri.org/", Order=0)]
        public TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionResponseBody Body;
        
        public CreateDebitElectronicCheckTransactionResponse() {
        }
        
        public CreateDebitElectronicCheckTransactionResponse(TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CreateDebitElectronicCheckTransactionResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public TestUnit.CCardProcessorService.ArrayOfString CreateDebitElectronicCheckTransactionResult;
        
        public CreateDebitElectronicCheckTransactionResponseBody() {
        }
        
        public CreateDebitElectronicCheckTransactionResponseBody(TestUnit.CCardProcessorService.ArrayOfString CreateDebitElectronicCheckTransactionResult) {
            this.CreateDebitElectronicCheckTransactionResult = CreateDebitElectronicCheckTransactionResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface ProcessorSoapChannel : TestUnit.CCardProcessorService.ProcessorSoap, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class ProcessorSoapClient : System.ServiceModel.ClientBase<TestUnit.CCardProcessorService.ProcessorSoap>, TestUnit.CCardProcessorService.ProcessorSoap {
        
        public ProcessorSoapClient() {
        }
        
        public ProcessorSoapClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public ProcessorSoapClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ProcessorSoapClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ProcessorSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.CreateCreditCardProfileResponse TestUnit.CCardProcessorService.ProcessorSoap.CreateCreditCardProfile(TestUnit.CCardProcessorService.CreateCreditCardProfileRequest request) {
            return base.Channel.CreateCreditCardProfile(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString CreateCreditCardProfile(
                    string profileReferenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string ccardNumber, 
                    string cvNumber, 
                    string cardType, 
                    string expMonth, 
                    string expYear, 
                    string currency) {
            TestUnit.CCardProcessorService.CreateCreditCardProfileRequest inValue = new TestUnit.CCardProcessorService.CreateCreditCardProfileRequest();
            inValue.Body = new TestUnit.CCardProcessorService.CreateCreditCardProfileRequestBody();
            inValue.Body.profileReferenceCode = profileReferenceCode;
            inValue.Body.firstName = firstName;
            inValue.Body.lastName = lastName;
            inValue.Body.companyName = companyName;
            inValue.Body.address1 = address1;
            inValue.Body.address2 = address2;
            inValue.Body.city = city;
            inValue.Body.state = state;
            inValue.Body.zipCode = zipCode;
            inValue.Body.country = country;
            inValue.Body.phoneNumber = phoneNumber;
            inValue.Body.email = email;
            inValue.Body.ccardNumber = ccardNumber;
            inValue.Body.cvNumber = cvNumber;
            inValue.Body.cardType = cardType;
            inValue.Body.expMonth = expMonth;
            inValue.Body.expYear = expYear;
            inValue.Body.currency = currency;
            TestUnit.CCardProcessorService.CreateCreditCardProfileResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).CreateCreditCardProfile(inValue);
            return retVal.Body.CreateCreditCardProfileResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateCreditCardProfileResponse> TestUnit.CCardProcessorService.ProcessorSoap.CreateCreditCardProfileAsync(TestUnit.CCardProcessorService.CreateCreditCardProfileRequest request) {
            return base.Channel.CreateCreditCardProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateCreditCardProfileResponse> CreateCreditCardProfileAsync(
                    string profileReferenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string ccardNumber, 
                    string cvNumber, 
                    string cardType, 
                    string expMonth, 
                    string expYear, 
                    string currency) {
            TestUnit.CCardProcessorService.CreateCreditCardProfileRequest inValue = new TestUnit.CCardProcessorService.CreateCreditCardProfileRequest();
            inValue.Body = new TestUnit.CCardProcessorService.CreateCreditCardProfileRequestBody();
            inValue.Body.profileReferenceCode = profileReferenceCode;
            inValue.Body.firstName = firstName;
            inValue.Body.lastName = lastName;
            inValue.Body.companyName = companyName;
            inValue.Body.address1 = address1;
            inValue.Body.address2 = address2;
            inValue.Body.city = city;
            inValue.Body.state = state;
            inValue.Body.zipCode = zipCode;
            inValue.Body.country = country;
            inValue.Body.phoneNumber = phoneNumber;
            inValue.Body.email = email;
            inValue.Body.ccardNumber = ccardNumber;
            inValue.Body.cvNumber = cvNumber;
            inValue.Body.cardType = cardType;
            inValue.Body.expMonth = expMonth;
            inValue.Body.expYear = expYear;
            inValue.Body.currency = currency;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).CreateCreditCardProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.AuthorizeCreditcardResponse TestUnit.CCardProcessorService.ProcessorSoap.AuthorizeCreditcard(TestUnit.CCardProcessorService.AuthorizeCreditcardRequest request) {
            return base.Channel.AuthorizeCreditcard(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString AuthorizeCreditcard(string referenceCode, string suscriptionID) {
            TestUnit.CCardProcessorService.AuthorizeCreditcardRequest inValue = new TestUnit.CCardProcessorService.AuthorizeCreditcardRequest();
            inValue.Body = new TestUnit.CCardProcessorService.AuthorizeCreditcardRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.suscriptionID = suscriptionID;
            TestUnit.CCardProcessorService.AuthorizeCreditcardResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).AuthorizeCreditcard(inValue);
            return retVal.Body.AuthorizeCreditcardResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.AuthorizeCreditcardResponse> TestUnit.CCardProcessorService.ProcessorSoap.AuthorizeCreditcardAsync(TestUnit.CCardProcessorService.AuthorizeCreditcardRequest request) {
            return base.Channel.AuthorizeCreditcardAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.AuthorizeCreditcardResponse> AuthorizeCreditcardAsync(string referenceCode, string suscriptionID) {
            TestUnit.CCardProcessorService.AuthorizeCreditcardRequest inValue = new TestUnit.CCardProcessorService.AuthorizeCreditcardRequest();
            inValue.Body = new TestUnit.CCardProcessorService.AuthorizeCreditcardRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.suscriptionID = suscriptionID;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).AuthorizeCreditcardAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.ReverseAuthorizationResponse TestUnit.CCardProcessorService.ProcessorSoap.ReverseAuthorization(TestUnit.CCardProcessorService.ReverseAuthorizationRequest request) {
            return base.Channel.ReverseAuthorization(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString ReverseAuthorization(string referenceCode, string authorizationRequestID, string currency) {
            TestUnit.CCardProcessorService.ReverseAuthorizationRequest inValue = new TestUnit.CCardProcessorService.ReverseAuthorizationRequest();
            inValue.Body = new TestUnit.CCardProcessorService.ReverseAuthorizationRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.authorizationRequestID = authorizationRequestID;
            inValue.Body.currency = currency;
            TestUnit.CCardProcessorService.ReverseAuthorizationResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).ReverseAuthorization(inValue);
            return retVal.Body.ReverseAuthorizationResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.ReverseAuthorizationResponse> TestUnit.CCardProcessorService.ProcessorSoap.ReverseAuthorizationAsync(TestUnit.CCardProcessorService.ReverseAuthorizationRequest request) {
            return base.Channel.ReverseAuthorizationAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.ReverseAuthorizationResponse> ReverseAuthorizationAsync(string referenceCode, string authorizationRequestID, string currency) {
            TestUnit.CCardProcessorService.ReverseAuthorizationRequest inValue = new TestUnit.CCardProcessorService.ReverseAuthorizationRequest();
            inValue.Body = new TestUnit.CCardProcessorService.ReverseAuthorizationRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.authorizationRequestID = authorizationRequestID;
            inValue.Body.currency = currency;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).ReverseAuthorizationAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.UpdateProfileInformationResponse TestUnit.CCardProcessorService.ProcessorSoap.UpdateProfileInformation(TestUnit.CCardProcessorService.UpdateProfileInformationRequest request) {
            return base.Channel.UpdateProfileInformation(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString UpdateProfileInformation(string subscriptionID, string referenceCode, string firstName, string lastName, string companyName, string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email) {
            TestUnit.CCardProcessorService.UpdateProfileInformationRequest inValue = new TestUnit.CCardProcessorService.UpdateProfileInformationRequest();
            inValue.Body = new TestUnit.CCardProcessorService.UpdateProfileInformationRequestBody();
            inValue.Body.subscriptionID = subscriptionID;
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.firstName = firstName;
            inValue.Body.lastName = lastName;
            inValue.Body.companyName = companyName;
            inValue.Body.address1 = address1;
            inValue.Body.address2 = address2;
            inValue.Body.city = city;
            inValue.Body.state = state;
            inValue.Body.zipCode = zipCode;
            inValue.Body.country = country;
            inValue.Body.phoneNumber = phoneNumber;
            inValue.Body.email = email;
            TestUnit.CCardProcessorService.UpdateProfileInformationResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).UpdateProfileInformation(inValue);
            return retVal.Body.UpdateProfileInformationResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateProfileInformationResponse> TestUnit.CCardProcessorService.ProcessorSoap.UpdateProfileInformationAsync(TestUnit.CCardProcessorService.UpdateProfileInformationRequest request) {
            return base.Channel.UpdateProfileInformationAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateProfileInformationResponse> UpdateProfileInformationAsync(string subscriptionID, string referenceCode, string firstName, string lastName, string companyName, string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email) {
            TestUnit.CCardProcessorService.UpdateProfileInformationRequest inValue = new TestUnit.CCardProcessorService.UpdateProfileInformationRequest();
            inValue.Body = new TestUnit.CCardProcessorService.UpdateProfileInformationRequestBody();
            inValue.Body.subscriptionID = subscriptionID;
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.firstName = firstName;
            inValue.Body.lastName = lastName;
            inValue.Body.companyName = companyName;
            inValue.Body.address1 = address1;
            inValue.Body.address2 = address2;
            inValue.Body.city = city;
            inValue.Body.state = state;
            inValue.Body.zipCode = zipCode;
            inValue.Body.country = country;
            inValue.Body.phoneNumber = phoneNumber;
            inValue.Body.email = email;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).UpdateProfileInformationAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.UpdateCreditCardInformationResponse TestUnit.CCardProcessorService.ProcessorSoap.UpdateCreditCardInformation(TestUnit.CCardProcessorService.UpdateCreditCardInformationRequest request) {
            return base.Channel.UpdateCreditCardInformation(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString UpdateCreditCardInformation(string subscriptionID, string referenceCode, string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear) {
            TestUnit.CCardProcessorService.UpdateCreditCardInformationRequest inValue = new TestUnit.CCardProcessorService.UpdateCreditCardInformationRequest();
            inValue.Body = new TestUnit.CCardProcessorService.UpdateCreditCardInformationRequestBody();
            inValue.Body.subscriptionID = subscriptionID;
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.ccardNumber = ccardNumber;
            inValue.Body.cvNumber = cvNumber;
            inValue.Body.cardType = cardType;
            inValue.Body.expMonth = expMonth;
            inValue.Body.expYear = expYear;
            TestUnit.CCardProcessorService.UpdateCreditCardInformationResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).UpdateCreditCardInformation(inValue);
            return retVal.Body.UpdateCreditCardInformationResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateCreditCardInformationResponse> TestUnit.CCardProcessorService.ProcessorSoap.UpdateCreditCardInformationAsync(TestUnit.CCardProcessorService.UpdateCreditCardInformationRequest request) {
            return base.Channel.UpdateCreditCardInformationAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateCreditCardInformationResponse> UpdateCreditCardInformationAsync(string subscriptionID, string referenceCode, string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear) {
            TestUnit.CCardProcessorService.UpdateCreditCardInformationRequest inValue = new TestUnit.CCardProcessorService.UpdateCreditCardInformationRequest();
            inValue.Body = new TestUnit.CCardProcessorService.UpdateCreditCardInformationRequestBody();
            inValue.Body.subscriptionID = subscriptionID;
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.ccardNumber = ccardNumber;
            inValue.Body.cvNumber = cvNumber;
            inValue.Body.cardType = cardType;
            inValue.Body.expMonth = expMonth;
            inValue.Body.expYear = expYear;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).UpdateCreditCardInformationAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.CancelProfileResponse TestUnit.CCardProcessorService.ProcessorSoap.CancelProfile(TestUnit.CCardProcessorService.CancelProfileRequest request) {
            return base.Channel.CancelProfile(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString CancelProfile(string subscriptionID, string profileReferenceCode) {
            TestUnit.CCardProcessorService.CancelProfileRequest inValue = new TestUnit.CCardProcessorService.CancelProfileRequest();
            inValue.Body = new TestUnit.CCardProcessorService.CancelProfileRequestBody();
            inValue.Body.subscriptionID = subscriptionID;
            inValue.Body.profileReferenceCode = profileReferenceCode;
            TestUnit.CCardProcessorService.CancelProfileResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).CancelProfile(inValue);
            return retVal.Body.CancelProfileResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CancelProfileResponse> TestUnit.CCardProcessorService.ProcessorSoap.CancelProfileAsync(TestUnit.CCardProcessorService.CancelProfileRequest request) {
            return base.Channel.CancelProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CancelProfileResponse> CancelProfileAsync(string subscriptionID, string profileReferenceCode) {
            TestUnit.CCardProcessorService.CancelProfileRequest inValue = new TestUnit.CCardProcessorService.CancelProfileRequest();
            inValue.Body = new TestUnit.CCardProcessorService.CancelProfileRequestBody();
            inValue.Body.subscriptionID = subscriptionID;
            inValue.Body.profileReferenceCode = profileReferenceCode;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).CancelProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.ChargeMoneyResponse TestUnit.CCardProcessorService.ProcessorSoap.ChargeMoney(TestUnit.CCardProcessorService.ChargeMoneyRequest request) {
            return base.Channel.ChargeMoney(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString ChargeMoney(string referenceCode, string suscriptionID, string serviceName, string value) {
            TestUnit.CCardProcessorService.ChargeMoneyRequest inValue = new TestUnit.CCardProcessorService.ChargeMoneyRequest();
            inValue.Body = new TestUnit.CCardProcessorService.ChargeMoneyRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.suscriptionID = suscriptionID;
            inValue.Body.serviceName = serviceName;
            inValue.Body.value = value;
            TestUnit.CCardProcessorService.ChargeMoneyResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).ChargeMoney(inValue);
            return retVal.Body.ChargeMoneyResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.ChargeMoneyResponse> TestUnit.CCardProcessorService.ProcessorSoap.ChargeMoneyAsync(TestUnit.CCardProcessorService.ChargeMoneyRequest request) {
            return base.Channel.ChargeMoneyAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.ChargeMoneyResponse> ChargeMoneyAsync(string referenceCode, string suscriptionID, string serviceName, string value) {
            TestUnit.CCardProcessorService.ChargeMoneyRequest inValue = new TestUnit.CCardProcessorService.ChargeMoneyRequest();
            inValue.Body = new TestUnit.CCardProcessorService.ChargeMoneyRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.suscriptionID = suscriptionID;
            inValue.Body.serviceName = serviceName;
            inValue.Body.value = value;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).ChargeMoneyAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileResponse TestUnit.CCardProcessorService.ProcessorSoap.CreateDebitElectronicCheckProfile(TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequest request) {
            return base.Channel.CreateDebitElectronicCheckProfile(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString CreateDebitElectronicCheckProfile(
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequest inValue = new TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequest();
            inValue.Body = new TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.firstName = firstName;
            inValue.Body.lastName = lastName;
            inValue.Body.companyName = companyName;
            inValue.Body.dateOfBirth = dateOfBirth;
            inValue.Body.address1 = address1;
            inValue.Body.address2 = address2;
            inValue.Body.city = city;
            inValue.Body.state = state;
            inValue.Body.zipCode = zipCode;
            inValue.Body.country = country;
            inValue.Body.phoneNumber = phoneNumber;
            inValue.Body.email = email;
            inValue.Body.accountNumber = accountNumber;
            inValue.Body.accountType = accountType;
            inValue.Body.bankTransitNumber = bankTransitNumber;
            inValue.Body.currency = currency;
            TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).CreateDebitElectronicCheckProfile(inValue);
            return retVal.Body.CreateDebitElectronicCheckProfileResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileResponse> TestUnit.CCardProcessorService.ProcessorSoap.CreateDebitElectronicCheckProfileAsync(TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequest request) {
            return base.Channel.CreateDebitElectronicCheckProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileResponse> CreateDebitElectronicCheckProfileAsync(
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequest inValue = new TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequest();
            inValue.Body = new TestUnit.CCardProcessorService.CreateDebitElectronicCheckProfileRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.firstName = firstName;
            inValue.Body.lastName = lastName;
            inValue.Body.companyName = companyName;
            inValue.Body.dateOfBirth = dateOfBirth;
            inValue.Body.address1 = address1;
            inValue.Body.address2 = address2;
            inValue.Body.city = city;
            inValue.Body.state = state;
            inValue.Body.zipCode = zipCode;
            inValue.Body.country = country;
            inValue.Body.phoneNumber = phoneNumber;
            inValue.Body.email = email;
            inValue.Body.accountNumber = accountNumber;
            inValue.Body.accountType = accountType;
            inValue.Body.bankTransitNumber = bankTransitNumber;
            inValue.Body.currency = currency;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).CreateDebitElectronicCheckProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileResponse TestUnit.CCardProcessorService.ProcessorSoap.UpdateDebitElectronicCheckProfile(TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequest request) {
            return base.Channel.UpdateDebitElectronicCheckProfile(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString UpdateDebitElectronicCheckProfile(
                    string subscriptionID, 
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequest inValue = new TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequest();
            inValue.Body = new TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequestBody();
            inValue.Body.subscriptionID = subscriptionID;
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.firstName = firstName;
            inValue.Body.lastName = lastName;
            inValue.Body.companyName = companyName;
            inValue.Body.dateOfBirth = dateOfBirth;
            inValue.Body.address1 = address1;
            inValue.Body.address2 = address2;
            inValue.Body.city = city;
            inValue.Body.state = state;
            inValue.Body.zipCode = zipCode;
            inValue.Body.country = country;
            inValue.Body.phoneNumber = phoneNumber;
            inValue.Body.email = email;
            inValue.Body.accountNumber = accountNumber;
            inValue.Body.accountType = accountType;
            inValue.Body.bankTransitNumber = bankTransitNumber;
            inValue.Body.currency = currency;
            TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).UpdateDebitElectronicCheckProfile(inValue);
            return retVal.Body.UpdateDebitElectronicCheckProfileResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileResponse> TestUnit.CCardProcessorService.ProcessorSoap.UpdateDebitElectronicCheckProfileAsync(TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequest request) {
            return base.Channel.UpdateDebitElectronicCheckProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileResponse> UpdateDebitElectronicCheckProfileAsync(
                    string subscriptionID, 
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequest inValue = new TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequest();
            inValue.Body = new TestUnit.CCardProcessorService.UpdateDebitElectronicCheckProfileRequestBody();
            inValue.Body.subscriptionID = subscriptionID;
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.firstName = firstName;
            inValue.Body.lastName = lastName;
            inValue.Body.companyName = companyName;
            inValue.Body.dateOfBirth = dateOfBirth;
            inValue.Body.address1 = address1;
            inValue.Body.address2 = address2;
            inValue.Body.city = city;
            inValue.Body.state = state;
            inValue.Body.zipCode = zipCode;
            inValue.Body.country = country;
            inValue.Body.phoneNumber = phoneNumber;
            inValue.Body.email = email;
            inValue.Body.accountNumber = accountNumber;
            inValue.Body.accountType = accountType;
            inValue.Body.bankTransitNumber = bankTransitNumber;
            inValue.Body.currency = currency;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).UpdateDebitElectronicCheckProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionResponse TestUnit.CCardProcessorService.ProcessorSoap.CreateDebitElectronicCheckTransaction(TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequest request) {
            return base.Channel.CreateDebitElectronicCheckTransaction(request);
        }
        
        public TestUnit.CCardProcessorService.ArrayOfString CreateDebitElectronicCheckTransaction(string referenceCode, string suscriptionID, string checkNumber, string serviceName, string value) {
            TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequest inValue = new TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequest();
            inValue.Body = new TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.suscriptionID = suscriptionID;
            inValue.Body.checkNumber = checkNumber;
            inValue.Body.serviceName = serviceName;
            inValue.Body.value = value;
            TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionResponse retVal = ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).CreateDebitElectronicCheckTransaction(inValue);
            return retVal.Body.CreateDebitElectronicCheckTransactionResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionResponse> TestUnit.CCardProcessorService.ProcessorSoap.CreateDebitElectronicCheckTransactionAsync(TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequest request) {
            return base.Channel.CreateDebitElectronicCheckTransactionAsync(request);
        }
        
        public System.Threading.Tasks.Task<TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionResponse> CreateDebitElectronicCheckTransactionAsync(string referenceCode, string suscriptionID, string checkNumber, string serviceName, string value) {
            TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequest inValue = new TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequest();
            inValue.Body = new TestUnit.CCardProcessorService.CreateDebitElectronicCheckTransactionRequestBody();
            inValue.Body.referenceCode = referenceCode;
            inValue.Body.suscriptionID = suscriptionID;
            inValue.Body.checkNumber = checkNumber;
            inValue.Body.serviceName = serviceName;
            inValue.Body.value = value;
            return ((TestUnit.CCardProcessorService.ProcessorSoap)(this)).CreateDebitElectronicCheckTransactionAsync(inValue);
        }
    }
}
