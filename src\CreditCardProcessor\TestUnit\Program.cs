using System;
using TestUnit.CCardProcessorService;

namespace TestUnit {
	class Program {
		static void Main(string[] args) {
			CreditCardTestUnitCasesVisa(false);
			//CreditCardTestUnitCasesAmericanExpress();
			//CreditCardTestUnitCasesMasterCard();
			//CreditCardTestUnitDiscover();
			//CreditCardTestUnitDinersClub();
			//CreditCardTestUnitJCB();
			//ElectronicCheckTestUnitCases();
			//Temp();
		}

		private static void CreditCardTestUnitCasesVisa(bool includeFulfillmentValidation) {
			Processor ccardProcessor = new Processor();
			string[] result = null;

			#region Create Profile
			if (includeFulfillmentValidation) {
				// Check validation for Create Credit Card Profile
				Console.WriteLine("Create Profile (Empty) (CC number is invalid) ...");
				result = ccardProcessor.CreateCreditCardProfile("CreateProfile",
					"", "", "", "", "", "", "", "", "",
					"", "",
				"", "", "", "", "", "");
				Console.WriteLine("Result Code - '" + result[0] + "'");
				Console.WriteLine("Result String - '" + result[1] + "'");
				Console.WriteLine("Request ID - '" + result[2] + "'");
				Console.WriteLine("Request Token - '" + result[3] + "'");
				Console.ReadLine();
			}
			Console.WriteLine("CreateProfile (CC number is invalid) ...");
			result = ccardProcessor.CreateCreditCardProfile("CreateProfile(Visa)",
				"TestFirst111", "TestLast111", "DealerN02", "6152 Mountain Vista", "Apt 333", "Henderson", "NV", "89014", "US",
				"************", "<EMAIL>",
			"****************", "123", "001", "12", "2030", "USD");

			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
			string profileSubscriptionIDVisa = result[4];
			#endregion

			#region Update CC information
			if (includeFulfillmentValidation) {
				// Check validation for Create Credit Card Transaction
				Console.WriteLine("Testing Update (Empty) CC information for profile - '" + profileSubscriptionIDVisa + "'...");
				result = ccardProcessor.UpdateCreditCardInformation("",
					"", "", "", "", "", "");
				Console.WriteLine("Result Code - '" + result[0] + "'");
				Console.WriteLine("Result String - '" + result[1] + "'");
				Console.WriteLine("Request ID - '" + result[2] + "'");
				Console.WriteLine("Request Token - '" + result[3] + "'");
				Console.ReadLine();
			}

			Console.WriteLine("Testing Update CC information for profile - '" + profileSubscriptionIDVisa + "'...");
			result = ccardProcessor.UpdateCreditCardInformation(profileSubscriptionIDVisa, "UpdateVisaNumber",
				"****************", "123", "001", "01", "2030");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			Console.WriteLine("Testing Update CC information for profile - '" + profileSubscriptionIDVisa + "'...");
			result = ccardProcessor.UpdateCreditCardInformation(profileSubscriptionIDVisa, "UpdateVisaNumber",
				"", "", "", "01", "2030");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
			#endregion

			#region Authorize CC
			if (includeFulfillmentValidation) {
				Console.WriteLine("Testing Authorization (Empty) for updated CC Card. '" + profileSubscriptionIDVisa + "' profile...");
				result = ccardProcessor.AuthorizeCreditcard("",
					"");
				Console.WriteLine("Result Code - '" + result[0] + "'");
				Console.WriteLine("Result String - '" + result[1] + "'");
				Console.WriteLine("Request ID - '" + result[2] + "'");
				Console.WriteLine("Request Token - '" + result[3] + "'");
				Console.ReadLine();
			}
			Console.WriteLine("Testing Authorization for updated CC Card. '" + profileSubscriptionIDVisa + "' profile...");
			result = ccardProcessor.AuthorizeCreditcard("AuthorizationWithValidCC",
				profileSubscriptionIDVisa);
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
			string reversalRequestID = result[2];
			string reversalToken = result[3];
			#endregion

			#region Reversa Authorization
			if (includeFulfillmentValidation) {
				Console.WriteLine("Testing Reversing (Empty) for '" + profileSubscriptionIDVisa + "' profile...");
				result = ccardProcessor.ReverseAuthorization("", "", "");
				Console.WriteLine("Result Code - '" + result[0] + "'");
				Console.WriteLine("Result String - '" + result[1] + "'");
				Console.WriteLine("Request ID - '" + result[2] + "'");
				Console.WriteLine("Request Token - '" + result[3] + "'");
				Console.ReadLine();
			}
			Console.WriteLine("Testing Reversing for '" + profileSubscriptionIDVisa + "' profile...");
			result = ccardProcessor.ReverseAuthorization("ReverseAuth", reversalRequestID, "USD");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
			#endregion

			#region Update Profile information
			Console.WriteLine("Testing Update Profile information for profile - '" + profileSubscriptionIDVisa + "'...");
			result = ccardProcessor.UpdateProfileInformation(profileSubscriptionIDVisa, "UpdateVisaNumber2",
				"TestFirst", "TestLast", "", "", "Apt 222", "", "", "", "",
				"************", "");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
			#endregion

			#region Charge Money
			if (includeFulfillmentValidation) {
				Console.WriteLine("Testing Credit Transaction (Empty) '" + profileSubscriptionIDVisa + "'...");
				result = ccardProcessor.ChargeMoney("",
					"", "", "");
				Console.WriteLine("Result Code - '" + result[0] + "'");
				Console.WriteLine("Result String - '" + result[1] + "'");
				Console.WriteLine("Request ID - '" + result[2] + "'");
				Console.WriteLine("Request Token - '" + result[3] + "'");
				Console.ReadLine();
			}
			// Create Credit Card Transaction for updated profile - Visa
			Console.WriteLine("Testing Credit Transaction '" + profileSubscriptionIDVisa + "'...");
			result = ccardProcessor.ChargeMoney("ChargeProfile",
				profileSubscriptionIDVisa, "March 2030", "300");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
			#endregion

			#region Cancel Profile
			if (includeFulfillmentValidation) {
				// Testing Validation for Closing Profile
				Console.WriteLine("Testing Validation Cancel '' profile...");
				result = ccardProcessor.CancelProfile("", "");
				Console.WriteLine("Result Code - '" + result[0] + "'");
				Console.WriteLine("Result String - '" + result[1] + "'");
				Console.WriteLine("Request ID - '" + result[2] + "'");
				Console.WriteLine("Request Token - '" + result[3] + "'");
				Console.ReadLine();
			}
			// Cancel Credit Card Profile (Visa)
			Console.WriteLine("Cancel '" + profileSubscriptionIDVisa + "' profile...");
			result = ccardProcessor.CancelProfile(profileSubscriptionIDVisa, "DealerTestProfile(Visa)");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
			#endregion
		}

		private static void CreditCardTestUnitCasesAmericanExpress() {
			Processor ccardProcessor = new Processor();
			string[] result = null;

			// Create Credit Card Profile - AmericanExpress
			Console.WriteLine("Testing CC Profile Creation (AmericanExpress) ...");
			result = ccardProcessor.CreateCreditCardProfile("DealerTestProfile(AmericanExpress)",
				"TestFirstAE", "TestFirstAE", "DealerN02", "6151 Mountain Vista", "Apt 555", "Henderson", "NV", "89014", "US",
				"702-555-555", "<EMAIL>",
			"***************", "12", "003", "12", "2030", "USD");

			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			string profileSubscriptionIDAmericanExpress = result[4];
			// Create Credit Card Transaction - AmericanExpress
			Console.WriteLine("Testing Credit Transaction (AmericanExpress) for created '" + profileSubscriptionIDAmericanExpress + "'...");
			result = ccardProcessor.ChargeMoney("February_Payment7890",
				profileSubscriptionIDAmericanExpress, "February 2030", "399");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			// Cancel Credit Card Profile (American Express)
			Console.WriteLine("Cancel '" + profileSubscriptionIDAmericanExpress + "' profile...");
			result = ccardProcessor.CancelProfile(profileSubscriptionIDAmericanExpress, "DealerTestProfile(AmericanExpress) Close");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
		}

		private static void CreditCardTestUnitCasesMasterCard() {
			Processor ccardProcessor = new Processor();
			string[] result = null;

			// Create Credit Card Profile - Master Card
			Console.WriteLine("Testing CC Profile Creation (Master Card) ...");
			result = ccardProcessor.CreateCreditCardProfile("DealerTestProfile(MasterCard)",
				"TestFirstMA", "TestFirstMA", "DealerN03", "6151 Mountain Vista", "Apt 777", "Henderson", "NV", "89014", "US",
				"************", "<EMAIL>",
			"****************", "123", "002", "12", "2030", "USD");

			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			string profileSubscriptionIDMasterCard = result[4];
			// Create Credit Card Transaction - AmericanExpress
			Console.WriteLine("Testing Credit Transaction (Master Card) for created '" + profileSubscriptionIDMasterCard + "'...");
			result = ccardProcessor.ChargeMoney("DealerTestProfile(MasterCard)-February_Payment",
				profileSubscriptionIDMasterCard, "February 2030", "300");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			// Cancel Credit Card Profile (American Express)
			Console.WriteLine("Cancel '" + profileSubscriptionIDMasterCard + "' profile...");
			result = ccardProcessor.CancelProfile(profileSubscriptionIDMasterCard, "DealerTestProfile(Master Card) Close");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
		}

		private static void CreditCardTestUnitDiscover() {
			Processor ccardProcessor = new Processor();
			string[] result = null;

			// Create Credit Card Profile - Discover
			Console.WriteLine("Testing CC Profile Creation (Discover) ...");
			result = ccardProcessor.CreateCreditCardProfile("DealerTestProfile(Discover)",
				"TestFirstDE", "TestFirstDE", "DealerN04", "6151 Mountain Vista", "Apt 888", "Henderson", "NV", "89014", "US",
				"************", "<EMAIL>",
			"****************", "123", "004", "12", "2030", "USD");

			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			string profileSubscriptionIDDescaver = result[4];
			// Create Credit Card Transaction - Discover
			Console.WriteLine("Testing Credit Transaction (Discover) for created '" + profileSubscriptionIDDescaver + "'...");
			result = ccardProcessor.ChargeMoney("DealerTestProfile(Discover)-February_Payment",
				profileSubscriptionIDDescaver, "February 2030", "300");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			// Cancel Credit Card Profile (Discover)
			Console.WriteLine("Cancel '" + profileSubscriptionIDDescaver + "' profile...");
			result = ccardProcessor.CancelProfile(profileSubscriptionIDDescaver, "DealerTestProfile(Discover) Close");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
		}

		private static void CreditCardTestUnitDinersClub() {
			Processor ccardProcessor = new Processor();
			string[] result = null;

			// Create Credit Card Profile - Diners Club
			Console.WriteLine("Testing CC Profile Creation (Diners Club) ...");
			result = ccardProcessor.CreateCreditCardProfile("DealerTestProfile(DinersClub)",
				"TestFirstDC", "TestFirstDC", "DealerN04", "6151 Mountain Vista", "Apt 999", "Henderson", "NV", "89014", "US",
				"702-999-999", "<EMAIL>",
			"38520000023237", "123", "005", "12", "2030", "USD");

			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			string profileSubscriptionIDDinersClub = result[4];
			// Create Credit Card Transaction - Diners Club
			Console.WriteLine("Testing Credit Transaction (Discover) for created '" + profileSubscriptionIDDinersClub + "'...");
			result = ccardProcessor.ChargeMoney("DealerTestProfile(DinersClub)-February_Payment",
				profileSubscriptionIDDinersClub, "February 2030", "300");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			// Cancel Credit Card Profile (Diners Club)
			Console.WriteLine("Cancel '" + profileSubscriptionIDDinersClub + "' profile...");
			result = ccardProcessor.CancelProfile(profileSubscriptionIDDinersClub, "DealerTestProfile(DinersClub)Close");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
		}

		private static void CreditCardTestUnitJCB() {
			Processor ccardProcessor = new Processor();
			string[] result = null;

			// Create Credit Card Profile - JCB
			Console.WriteLine("Testing CC Profile Creation (JCB) ...");
			result = ccardProcessor.CreateCreditCardProfile("DealerTestProfile(JCB)",
				"TestFirstJCB ", "TestFirstJCB ", "DealerN04", "6151 Mountain Vista", "Apt 000", "Henderson", "NV", "89014", "US",
				"************", "<EMAIL>",
			"3530111333300000", "123", "007", "12", "2030", "USD");

			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			string profileSubscriptionIDJCB = result[4];
			// Create Credit Card Transaction - JCB
			Console.WriteLine("Testing Credit Transaction (JCB) for created '" + profileSubscriptionIDJCB + "'...");
			result = ccardProcessor.ChargeMoney("DealerTestProfile(JCB)-February_Payment",
				profileSubscriptionIDJCB, "February 2030", "300");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			// Cancel Credit Card Profile (JCB)
			Console.WriteLine("Cancel '" + profileSubscriptionIDJCB + "' profile...");
			result = ccardProcessor.CancelProfile(profileSubscriptionIDJCB, "DealerTestProfile(JCB)Close");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
		}

		private static void ElectronicCheckTestUnitCases() {
			Processor ccardProcessor = new Processor();
			string[] result = null;

			Console.WriteLine("Testing Electronic Check Profile Creation(Corporate)...");
			result = ccardProcessor.CreateDebitElectronicCheckProfile("DealerElectronicTestProfile",
				"TestFirst1", "TestFirst1", "Test Inc", "",
				"6152 Mountain Vista", "Apt 333", "Henderson", "NV", "89014", "US",
				"************", "<EMAIL>",
				"4100000000", "X", "071923284", "USD");

			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			string profileSubscriptionECheckCorporate = result[2];

			Console.WriteLine("Testing Electronic Check create transaction for '" + profileSubscriptionECheckCorporate + "'...");
			result = ccardProcessor.CreateDebitElectronicCheckTransaction("February_Payment7890",
				profileSubscriptionECheckCorporate, "1002", "February 2030", "457.00");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			Console.WriteLine("Testing Electronic Check Update Profile for '" + profileSubscriptionECheckCorporate + "'...");
			result = ccardProcessor.UpdateDebitElectronicCheckProfile(profileSubscriptionECheckCorporate, "DealerElectronicTestProfile",
				"", "TestFirst", "Test Test Inc", "",
				"", "Apt 4444", "", "", "", "",
				"", "",
				"4200000000", "S", "071923284", "");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			Console.WriteLine("Testing Electronic Check create transaction for UPDATED '" + profileSubscriptionECheckCorporate + "'...");
			result = ccardProcessor.CreateDebitElectronicCheckTransaction("February_Payment7890",
				profileSubscriptionECheckCorporate, "1004", "February 2030", "557.00");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			Console.WriteLine("Testing Electronic Check Profile Creation (Personal) ...");
			result = ccardProcessor.CreateDebitElectronicCheckProfile("DealerElectronicTestProfile",
				"TestFirst", "TestLadt", "", "1975-02-21",
				"6152 Mountain Vista", "Apt 333", "Henderson", "NV", "89014", "US",
				"************", "<EMAIL>",
				"4100000000", "C", "071923284", "USD");

			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			string profileSubscriptionECheckPersonal = result[2];

			Console.WriteLine("Testing Electronic Check create transaction for personal '" + profileSubscriptionECheckPersonal + "'...");
			result = ccardProcessor.CreateDebitElectronicCheckTransaction("February_Payment7890",
				profileSubscriptionECheckPersonal, "1003", "February 2030", "557.00");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			// Cancel eCheck Profile Corporate
			Console.WriteLine("Cancel '" + profileSubscriptionECheckCorporate + "' profile...");
			result = ccardProcessor.CancelProfile(profileSubscriptionECheckCorporate, "DealerElectronicTestProfile");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();

			// Cancel eCheck Profile Parsonal
			Console.WriteLine("Cancel '" + profileSubscriptionECheckPersonal + "' profile...");
			result = ccardProcessor.CancelProfile(profileSubscriptionECheckPersonal, "DealerElectronicTestProfile");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
		}

		private static void Temp() {
			Processor ccardProcessor = new Processor();
			string[] result = null;

			string reversalRequestID = "1788300599380176174594";
			string reversalToken = "AHYJYPGoAFBJin4CEAU5toZX0gEFObaGV9IAmibZ";
			string profileSubscriptionIDVisa = "1788296747050176174594";
			Console.WriteLine("Testing Reversing for '" + profileSubscriptionIDVisa + "' profile...");
			result = ccardProcessor.ReverseAuthorization("ReverseAuth", reversalRequestID, "USD");
			Console.WriteLine("Result Code - '" + result[0] + "'");
			Console.WriteLine("Result String - '" + result[1] + "'");
			Console.WriteLine("Request ID - '" + result[2] + "'");
			Console.WriteLine("Request Token - '" + result[3] + "'");
			Console.ReadLine();
		}
	}
}
