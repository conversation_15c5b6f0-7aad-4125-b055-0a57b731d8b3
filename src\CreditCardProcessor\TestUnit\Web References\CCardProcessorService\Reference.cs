﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace TestUnit.CCardProcessorService {
    using System.Diagnostics;
    using System;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Web.Services.Protocols;
    using System.Web.Services;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="ProcessorSoap", Namespace="http://tempuri.org/")]
    public partial class Processor : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback CreateCreditCardProfileOperationCompleted;
        
        private System.Threading.SendOrPostCallback AuthorizeCreditcardOperationCompleted;
        
        private System.Threading.SendOrPostCallback ReverseAuthorizationOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpdateProfileInformationOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpdateCreditCardInformationOperationCompleted;
        
        private System.Threading.SendOrPostCallback CancelProfileOperationCompleted;
        
        private System.Threading.SendOrPostCallback ChargeMoneyOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateDebitElectronicCheckProfileOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpdateDebitElectronicCheckProfileOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateDebitElectronicCheckTransactionOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public Processor() {
            this.Url = global::TestUnit.Properties.Settings.Default.TestUnit_CCardProcessorService_Processor;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event CreateCreditCardProfileCompletedEventHandler CreateCreditCardProfileCompleted;
        
        /// <remarks/>
        public event AuthorizeCreditcardCompletedEventHandler AuthorizeCreditcardCompleted;
        
        /// <remarks/>
        public event ReverseAuthorizationCompletedEventHandler ReverseAuthorizationCompleted;
        
        /// <remarks/>
        public event UpdateProfileInformationCompletedEventHandler UpdateProfileInformationCompleted;
        
        /// <remarks/>
        public event UpdateCreditCardInformationCompletedEventHandler UpdateCreditCardInformationCompleted;
        
        /// <remarks/>
        public event CancelProfileCompletedEventHandler CancelProfileCompleted;
        
        /// <remarks/>
        public event ChargeMoneyCompletedEventHandler ChargeMoneyCompleted;
        
        /// <remarks/>
        public event CreateDebitElectronicCheckProfileCompletedEventHandler CreateDebitElectronicCheckProfileCompleted;
        
        /// <remarks/>
        public event UpdateDebitElectronicCheckProfileCompletedEventHandler UpdateDebitElectronicCheckProfileCompleted;
        
        /// <remarks/>
        public event CreateDebitElectronicCheckTransactionCompletedEventHandler CreateDebitElectronicCheckTransactionCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateCreditCardProfile", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] CreateCreditCardProfile(
                    string profileReferenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string ccardNumber, 
                    string cvNumber, 
                    string cardType, 
                    string expMonth, 
                    string expYear, 
                    string currency) {
            object[] results = this.Invoke("CreateCreditCardProfile", new object[] {
                        profileReferenceCode,
                        firstName,
                        lastName,
                        companyName,
                        address1,
                        address2,
                        city,
                        state,
                        zipCode,
                        country,
                        phoneNumber,
                        email,
                        ccardNumber,
                        cvNumber,
                        cardType,
                        expMonth,
                        expYear,
                        currency});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void CreateCreditCardProfileAsync(
                    string profileReferenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string ccardNumber, 
                    string cvNumber, 
                    string cardType, 
                    string expMonth, 
                    string expYear, 
                    string currency) {
            this.CreateCreditCardProfileAsync(profileReferenceCode, firstName, lastName, companyName, address1, address2, city, state, zipCode, country, phoneNumber, email, ccardNumber, cvNumber, cardType, expMonth, expYear, currency, null);
        }
        
        /// <remarks/>
        public void CreateCreditCardProfileAsync(
                    string profileReferenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string ccardNumber, 
                    string cvNumber, 
                    string cardType, 
                    string expMonth, 
                    string expYear, 
                    string currency, 
                    object userState) {
            if ((this.CreateCreditCardProfileOperationCompleted == null)) {
                this.CreateCreditCardProfileOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateCreditCardProfileOperationCompleted);
            }
            this.InvokeAsync("CreateCreditCardProfile", new object[] {
                        profileReferenceCode,
                        firstName,
                        lastName,
                        companyName,
                        address1,
                        address2,
                        city,
                        state,
                        zipCode,
                        country,
                        phoneNumber,
                        email,
                        ccardNumber,
                        cvNumber,
                        cardType,
                        expMonth,
                        expYear,
                        currency}, this.CreateCreditCardProfileOperationCompleted, userState);
        }
        
        private void OnCreateCreditCardProfileOperationCompleted(object arg) {
            if ((this.CreateCreditCardProfileCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateCreditCardProfileCompleted(this, new CreateCreditCardProfileCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/AuthorizeCreditcard", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] AuthorizeCreditcard(string referenceCode, string suscriptionID) {
            object[] results = this.Invoke("AuthorizeCreditcard", new object[] {
                        referenceCode,
                        suscriptionID});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void AuthorizeCreditcardAsync(string referenceCode, string suscriptionID) {
            this.AuthorizeCreditcardAsync(referenceCode, suscriptionID, null);
        }
        
        /// <remarks/>
        public void AuthorizeCreditcardAsync(string referenceCode, string suscriptionID, object userState) {
            if ((this.AuthorizeCreditcardOperationCompleted == null)) {
                this.AuthorizeCreditcardOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAuthorizeCreditcardOperationCompleted);
            }
            this.InvokeAsync("AuthorizeCreditcard", new object[] {
                        referenceCode,
                        suscriptionID}, this.AuthorizeCreditcardOperationCompleted, userState);
        }
        
        private void OnAuthorizeCreditcardOperationCompleted(object arg) {
            if ((this.AuthorizeCreditcardCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.AuthorizeCreditcardCompleted(this, new AuthorizeCreditcardCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ReverseAuthorization", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] ReverseAuthorization(string referenceCode, string authorizationRequestID, string currency) {
            object[] results = this.Invoke("ReverseAuthorization", new object[] {
                        referenceCode,
                        authorizationRequestID,
                        currency});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void ReverseAuthorizationAsync(string referenceCode, string authorizationRequestID, string currency) {
            this.ReverseAuthorizationAsync(referenceCode, authorizationRequestID, currency, null);
        }
        
        /// <remarks/>
        public void ReverseAuthorizationAsync(string referenceCode, string authorizationRequestID, string currency, object userState) {
            if ((this.ReverseAuthorizationOperationCompleted == null)) {
                this.ReverseAuthorizationOperationCompleted = new System.Threading.SendOrPostCallback(this.OnReverseAuthorizationOperationCompleted);
            }
            this.InvokeAsync("ReverseAuthorization", new object[] {
                        referenceCode,
                        authorizationRequestID,
                        currency}, this.ReverseAuthorizationOperationCompleted, userState);
        }
        
        private void OnReverseAuthorizationOperationCompleted(object arg) {
            if ((this.ReverseAuthorizationCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ReverseAuthorizationCompleted(this, new ReverseAuthorizationCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpdateProfileInformation", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] UpdateProfileInformation(string subscriptionID, string referenceCode, string firstName, string lastName, string companyName, string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email) {
            object[] results = this.Invoke("UpdateProfileInformation", new object[] {
                        subscriptionID,
                        referenceCode,
                        firstName,
                        lastName,
                        companyName,
                        address1,
                        address2,
                        city,
                        state,
                        zipCode,
                        country,
                        phoneNumber,
                        email});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void UpdateProfileInformationAsync(string subscriptionID, string referenceCode, string firstName, string lastName, string companyName, string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email) {
            this.UpdateProfileInformationAsync(subscriptionID, referenceCode, firstName, lastName, companyName, address1, address2, city, state, zipCode, country, phoneNumber, email, null);
        }
        
        /// <remarks/>
        public void UpdateProfileInformationAsync(string subscriptionID, string referenceCode, string firstName, string lastName, string companyName, string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email, object userState) {
            if ((this.UpdateProfileInformationOperationCompleted == null)) {
                this.UpdateProfileInformationOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpdateProfileInformationOperationCompleted);
            }
            this.InvokeAsync("UpdateProfileInformation", new object[] {
                        subscriptionID,
                        referenceCode,
                        firstName,
                        lastName,
                        companyName,
                        address1,
                        address2,
                        city,
                        state,
                        zipCode,
                        country,
                        phoneNumber,
                        email}, this.UpdateProfileInformationOperationCompleted, userState);
        }
        
        private void OnUpdateProfileInformationOperationCompleted(object arg) {
            if ((this.UpdateProfileInformationCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpdateProfileInformationCompleted(this, new UpdateProfileInformationCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpdateCreditCardInformation", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] UpdateCreditCardInformation(string subscriptionID, string referenceCode, string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear) {
            object[] results = this.Invoke("UpdateCreditCardInformation", new object[] {
                        subscriptionID,
                        referenceCode,
                        ccardNumber,
                        cvNumber,
                        cardType,
                        expMonth,
                        expYear});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void UpdateCreditCardInformationAsync(string subscriptionID, string referenceCode, string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear) {
            this.UpdateCreditCardInformationAsync(subscriptionID, referenceCode, ccardNumber, cvNumber, cardType, expMonth, expYear, null);
        }
        
        /// <remarks/>
        public void UpdateCreditCardInformationAsync(string subscriptionID, string referenceCode, string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear, object userState) {
            if ((this.UpdateCreditCardInformationOperationCompleted == null)) {
                this.UpdateCreditCardInformationOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpdateCreditCardInformationOperationCompleted);
            }
            this.InvokeAsync("UpdateCreditCardInformation", new object[] {
                        subscriptionID,
                        referenceCode,
                        ccardNumber,
                        cvNumber,
                        cardType,
                        expMonth,
                        expYear}, this.UpdateCreditCardInformationOperationCompleted, userState);
        }
        
        private void OnUpdateCreditCardInformationOperationCompleted(object arg) {
            if ((this.UpdateCreditCardInformationCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpdateCreditCardInformationCompleted(this, new UpdateCreditCardInformationCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CancelProfile", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] CancelProfile(string subscriptionID, string profileReferenceCode) {
            object[] results = this.Invoke("CancelProfile", new object[] {
                        subscriptionID,
                        profileReferenceCode});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void CancelProfileAsync(string subscriptionID, string profileReferenceCode) {
            this.CancelProfileAsync(subscriptionID, profileReferenceCode, null);
        }
        
        /// <remarks/>
        public void CancelProfileAsync(string subscriptionID, string profileReferenceCode, object userState) {
            if ((this.CancelProfileOperationCompleted == null)) {
                this.CancelProfileOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCancelProfileOperationCompleted);
            }
            this.InvokeAsync("CancelProfile", new object[] {
                        subscriptionID,
                        profileReferenceCode}, this.CancelProfileOperationCompleted, userState);
        }
        
        private void OnCancelProfileOperationCompleted(object arg) {
            if ((this.CancelProfileCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CancelProfileCompleted(this, new CancelProfileCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ChargeMoney", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] ChargeMoney(string referenceCode, string suscriptionID, string serviceName, string value) {
            object[] results = this.Invoke("ChargeMoney", new object[] {
                        referenceCode,
                        suscriptionID,
                        serviceName,
                        value});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void ChargeMoneyAsync(string referenceCode, string suscriptionID, string serviceName, string value) {
            this.ChargeMoneyAsync(referenceCode, suscriptionID, serviceName, value, null);
        }
        
        /// <remarks/>
        public void ChargeMoneyAsync(string referenceCode, string suscriptionID, string serviceName, string value, object userState) {
            if ((this.ChargeMoneyOperationCompleted == null)) {
                this.ChargeMoneyOperationCompleted = new System.Threading.SendOrPostCallback(this.OnChargeMoneyOperationCompleted);
            }
            this.InvokeAsync("ChargeMoney", new object[] {
                        referenceCode,
                        suscriptionID,
                        serviceName,
                        value}, this.ChargeMoneyOperationCompleted, userState);
        }
        
        private void OnChargeMoneyOperationCompleted(object arg) {
            if ((this.ChargeMoneyCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ChargeMoneyCompleted(this, new ChargeMoneyCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateDebitElectronicCheckProfile", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] CreateDebitElectronicCheckProfile(
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            object[] results = this.Invoke("CreateDebitElectronicCheckProfile", new object[] {
                        referenceCode,
                        firstName,
                        lastName,
                        companyName,
                        dateOfBirth,
                        address1,
                        address2,
                        city,
                        state,
                        zipCode,
                        country,
                        phoneNumber,
                        email,
                        accountNumber,
                        accountType,
                        bankTransitNumber,
                        currency});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void CreateDebitElectronicCheckProfileAsync(
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            this.CreateDebitElectronicCheckProfileAsync(referenceCode, firstName, lastName, companyName, dateOfBirth, address1, address2, city, state, zipCode, country, phoneNumber, email, accountNumber, accountType, bankTransitNumber, currency, null);
        }
        
        /// <remarks/>
        public void CreateDebitElectronicCheckProfileAsync(
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency, 
                    object userState) {
            if ((this.CreateDebitElectronicCheckProfileOperationCompleted == null)) {
                this.CreateDebitElectronicCheckProfileOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateDebitElectronicCheckProfileOperationCompleted);
            }
            this.InvokeAsync("CreateDebitElectronicCheckProfile", new object[] {
                        referenceCode,
                        firstName,
                        lastName,
                        companyName,
                        dateOfBirth,
                        address1,
                        address2,
                        city,
                        state,
                        zipCode,
                        country,
                        phoneNumber,
                        email,
                        accountNumber,
                        accountType,
                        bankTransitNumber,
                        currency}, this.CreateDebitElectronicCheckProfileOperationCompleted, userState);
        }
        
        private void OnCreateDebitElectronicCheckProfileOperationCompleted(object arg) {
            if ((this.CreateDebitElectronicCheckProfileCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateDebitElectronicCheckProfileCompleted(this, new CreateDebitElectronicCheckProfileCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpdateDebitElectronicCheckProfile", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] UpdateDebitElectronicCheckProfile(
                    string subscriptionID, 
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            object[] results = this.Invoke("UpdateDebitElectronicCheckProfile", new object[] {
                        subscriptionID,
                        referenceCode,
                        firstName,
                        lastName,
                        companyName,
                        dateOfBirth,
                        address1,
                        address2,
                        city,
                        state,
                        zipCode,
                        country,
                        phoneNumber,
                        email,
                        accountNumber,
                        accountType,
                        bankTransitNumber,
                        currency});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void UpdateDebitElectronicCheckProfileAsync(
                    string subscriptionID, 
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency) {
            this.UpdateDebitElectronicCheckProfileAsync(subscriptionID, referenceCode, firstName, lastName, companyName, dateOfBirth, address1, address2, city, state, zipCode, country, phoneNumber, email, accountNumber, accountType, bankTransitNumber, currency, null);
        }
        
        /// <remarks/>
        public void UpdateDebitElectronicCheckProfileAsync(
                    string subscriptionID, 
                    string referenceCode, 
                    string firstName, 
                    string lastName, 
                    string companyName, 
                    string dateOfBirth, 
                    string address1, 
                    string address2, 
                    string city, 
                    string state, 
                    string zipCode, 
                    string country, 
                    string phoneNumber, 
                    string email, 
                    string accountNumber, 
                    string accountType, 
                    string bankTransitNumber, 
                    string currency, 
                    object userState) {
            if ((this.UpdateDebitElectronicCheckProfileOperationCompleted == null)) {
                this.UpdateDebitElectronicCheckProfileOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpdateDebitElectronicCheckProfileOperationCompleted);
            }
            this.InvokeAsync("UpdateDebitElectronicCheckProfile", new object[] {
                        subscriptionID,
                        referenceCode,
                        firstName,
                        lastName,
                        companyName,
                        dateOfBirth,
                        address1,
                        address2,
                        city,
                        state,
                        zipCode,
                        country,
                        phoneNumber,
                        email,
                        accountNumber,
                        accountType,
                        bankTransitNumber,
                        currency}, this.UpdateDebitElectronicCheckProfileOperationCompleted, userState);
        }
        
        private void OnUpdateDebitElectronicCheckProfileOperationCompleted(object arg) {
            if ((this.UpdateDebitElectronicCheckProfileCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpdateDebitElectronicCheckProfileCompleted(this, new UpdateDebitElectronicCheckProfileCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateDebitElectronicCheckTransaction", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] CreateDebitElectronicCheckTransaction(string referenceCode, string suscriptionID, string checkNumber, string serviceName, string value) {
            object[] results = this.Invoke("CreateDebitElectronicCheckTransaction", new object[] {
                        referenceCode,
                        suscriptionID,
                        checkNumber,
                        serviceName,
                        value});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void CreateDebitElectronicCheckTransactionAsync(string referenceCode, string suscriptionID, string checkNumber, string serviceName, string value) {
            this.CreateDebitElectronicCheckTransactionAsync(referenceCode, suscriptionID, checkNumber, serviceName, value, null);
        }
        
        /// <remarks/>
        public void CreateDebitElectronicCheckTransactionAsync(string referenceCode, string suscriptionID, string checkNumber, string serviceName, string value, object userState) {
            if ((this.CreateDebitElectronicCheckTransactionOperationCompleted == null)) {
                this.CreateDebitElectronicCheckTransactionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateDebitElectronicCheckTransactionOperationCompleted);
            }
            this.InvokeAsync("CreateDebitElectronicCheckTransaction", new object[] {
                        referenceCode,
                        suscriptionID,
                        checkNumber,
                        serviceName,
                        value}, this.CreateDebitElectronicCheckTransactionOperationCompleted, userState);
        }
        
        private void OnCreateDebitElectronicCheckTransactionOperationCompleted(object arg) {
            if ((this.CreateDebitElectronicCheckTransactionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateDebitElectronicCheckTransactionCompleted(this, new CreateDebitElectronicCheckTransactionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void CreateCreditCardProfileCompletedEventHandler(object sender, CreateCreditCardProfileCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateCreditCardProfileCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateCreditCardProfileCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void AuthorizeCreditcardCompletedEventHandler(object sender, AuthorizeCreditcardCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AuthorizeCreditcardCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal AuthorizeCreditcardCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void ReverseAuthorizationCompletedEventHandler(object sender, ReverseAuthorizationCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ReverseAuthorizationCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ReverseAuthorizationCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void UpdateProfileInformationCompletedEventHandler(object sender, UpdateProfileInformationCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpdateProfileInformationCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpdateProfileInformationCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void UpdateCreditCardInformationCompletedEventHandler(object sender, UpdateCreditCardInformationCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpdateCreditCardInformationCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpdateCreditCardInformationCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void CancelProfileCompletedEventHandler(object sender, CancelProfileCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CancelProfileCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CancelProfileCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void ChargeMoneyCompletedEventHandler(object sender, ChargeMoneyCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ChargeMoneyCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ChargeMoneyCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void CreateDebitElectronicCheckProfileCompletedEventHandler(object sender, CreateDebitElectronicCheckProfileCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateDebitElectronicCheckProfileCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateDebitElectronicCheckProfileCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void UpdateDebitElectronicCheckProfileCompletedEventHandler(object sender, UpdateDebitElectronicCheckProfileCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpdateDebitElectronicCheckProfileCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpdateDebitElectronicCheckProfileCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void CreateDebitElectronicCheckTransactionCompletedEventHandler(object sender, CreateDebitElectronicCheckTransactionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateDebitElectronicCheckTransactionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateDebitElectronicCheckTransactionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591