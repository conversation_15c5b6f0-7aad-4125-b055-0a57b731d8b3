<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="TestUnit.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8.1"/></startup><system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="ProcessorSoap" />
            </basicHttpBinding>
        </bindings>
        <client>
            <endpoint address="http://ccardprocessor/Processor.asmx" binding="basicHttpBinding"
                bindingConfiguration="ProcessorSoap" contract="CCardProcessorService.ProcessorSoap"
                name="ProcessorSoap" />
        </client>
    </system.serviceModel>
    <applicationSettings>
        <TestUnit.Properties.Settings>
            <setting name="TestUnit_CCardProcessorService_Processor" serializeAs="String">
                <value>http://ccardprocessor/Processor.asmx</value>
            </setting>
        </TestUnit.Properties.Settings>
    </applicationSettings>
</configuration>
