﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35219.272
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestUnit", "TestUnit\TestUnit.csproj", "{19F74867-5053-4F7C-B24F-3415E0993CC7}"
EndProject
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "http://ccardprocessor", "http://ccardprocessor", "{DB9AFAEB-7544-401A-A8D1-2F02934154B5}"
	ProjectSection(WebsiteProperties) = preProject
		UseIISExpress = "false"
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.8.1"
		Debug.AspNetCompiler.VirtualPath = "/ccardprocessor"
		Debug.AspNetCompiler.PhysicalPath = "www\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\ccardprocessor\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/ccardprocessor"
		Release.AspNetCompiler.PhysicalPath = "www\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\ccardprocessor\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		SlnRelativePath = "www\"
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|.NET = Debug|.NET
		Debug|Any CPU = Debug|Any CPU
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Release|.NET = Release|.NET
		Release|Any CPU = Release|Any CPU
		Release|Mixed Platforms = Release|Mixed Platforms
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Debug|.NET.ActiveCfg = Debug|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Debug|.NET.Build.0 = Debug|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Release|.NET.ActiveCfg = Release|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Release|.NET.Build.0 = Release|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Release|Any CPU.Build.0 = Release|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{19F74867-5053-4F7C-B24F-3415E0993CC7}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Debug|.NET.ActiveCfg = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Debug|.NET.Build.0 = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Release|.NET.ActiveCfg = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Release|.NET.Build.0 = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Release|Any CPU.ActiveCfg = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Release|Any CPU.Build.0 = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Release|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{DB9AFAEB-7544-401A-A8D1-2F02934154B5}.Release|Mixed Platforms.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8628B72B-2817-49AE-8AF3-041DE4447C9D}
	EndGlobalSection
EndGlobal
