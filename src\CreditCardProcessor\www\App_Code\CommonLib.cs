public class CommonLib {
	public static string ProcessorTrueValue = "true";
	public static string ProcessorFalseValue = "false";

	public class RequestResult {
		public const int Total = 5;
		public const int ResultCodeIndex = 0;
		public const int ResultStringIndex = 1;
		public const int RequestIDIndex = 2;
		public const int RequestTokenIndex = 3;
		public const int SubscriptionIdIndex = 4;

		public string ResultCode = "-1";
		public string ResultString = string.Empty;
		public string RequestID = string.Empty;
		public string RequestToken = string.Empty;
		public string SubscriptionID = string.Empty;

		public static string[] GetStringArrayResult(RequestResult res) {
			string[] result = new string[Total];
			result[ResultCodeIndex] = res.ResultCode;
			result[ResultStringIndex] = res.ResultString;
			result[RequestIDIndex] = res.RequestID;
			result[RequestTokenIndex] = res.RequestToken;
			result[SubscriptionIdIndex] = res.SubscriptionID;
			return result;
		}

		public static string[] GetStringArrayResult(string resultCode, string resultString, string requestID, string requestToken, string subscriptionID) {
			string[] result = new string[Total];
			result[ResultCodeIndex] = resultCode;
			result[ResultStringIndex] = resultString;
			result[RequestIDIndex] = requestID;
			result[RequestTokenIndex] = requestToken;
			result[SubscriptionIdIndex] = subscriptionID;

			return result;
		}
	}
}
