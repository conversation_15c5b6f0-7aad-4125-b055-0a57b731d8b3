using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml;
using CyberSource.Clients;
using CyberSource.Clients.SoapServiceReference;

[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
public class Processor : System.Web.Services.WebService {
	private const string _preAuthorizeOperationName = "Pre-Authorize";
	private const string _preAuthorizeValue = "1.00";
	private const string _productNumber = "0";

	public Processor() {
		//Uncomment the following line if using designed components 
		//InitializeComponent(); 
	}

	#region Credit Cards
	[WebMethod]
	public string[] CreateCreditCardProfile(string profileReferenceCode,
		string firstName, string lastName, string companyName,
		string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email,
		string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear, string currency) {

		string validation = ValidateCreateCreditCardProfile(profileReferenceCode,
			firstName, lastName, companyName,
			address1, address2, city, state, zipCode, country, phoneNumber, email,
			ccardNumber, cvNumber, cardType, expMonth, expYear, currency);
		if (validation.Length > 0) {
			CommonLib.RequestResult res = new CommonLib.RequestResult();
			res.ResultString = validation;
			res.RequestID = profileReferenceCode;
			return CommonLib.RequestResult.GetStringArrayResult(res);
		}

		RequestMessage request = new RequestMessage();
		// add required fields
		// Mark the request as profile creator
		request.paySubscriptionCreateService = new PaySubscriptionCreateService();
		request.paySubscriptionCreateService.run = CommonLib.ProcessorTrueValue;

		request.paySubscriptionCreateService.disableAutoAuth = CommonLib.ProcessorTrueValue;

		request.businessRules = new BusinessRules();
		request.businessRules.ignoreAVSResult = CommonLib.ProcessorTrueValue;
		request.businessRules.ignoreCVResult = CommonLib.ProcessorFalseValue;

		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.automaticRenew = CommonLib.ProcessorFalseValue;
		request.recurringSubscriptionInfo.amount = "0.00";
		request.recurringSubscriptionInfo.frequency = "on-demand";

		InitRequestCommon(request, profileReferenceCode, firstName, lastName, companyName, address1, address2, city, state, zipCode, country, phoneNumber, email, currency);

		// card
		Card card = new Card();
		card.accountNumber = ccardNumber.Trim();
		card.expirationMonth = expMonth.Trim();
		card.expirationYear = expYear.Trim();
		card.cardType = cardType.Trim();
		if (cvNumber.Length > 0) {
			card.cvNumber = cvNumber.Trim();
		} else
			card.cvIndicator = "1";

		request.card = card;

		CommonLib.RequestResult result = ProcessRequest(request, profileReferenceCode);

		return CommonLib.RequestResult.GetStringArrayResult(result);
	}

	[WebMethod]
	public string[] AuthorizeCreditcard(string referenceCode,
		string suscriptionID) {

		if (referenceCode.Length == 0 || suscriptionID.Length == 0) {
			CommonLib.RequestResult res = new CommonLib.RequestResult();
			res.ResultString = "Reference Code and Subscripton ID can not be empty!";
			res.RequestID = referenceCode;
			return CommonLib.RequestResult.GetStringArrayResult(res);
		}

		RequestMessage request = new RequestMessage();

		request.merchantReferenceCode = referenceCode.Trim();

		request.ccAuthService = new CCAuthService();
		request.ccAuthService.run = CommonLib.ProcessorTrueValue;
		request.ccCaptureService = new CCCaptureService();
		request.ccCaptureService.run = CommonLib.ProcessorFalseValue;

		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.subscriptionID = suscriptionID.Trim();

		request.businessRules = new BusinessRules();
		request.businessRules.ignoreAVSResult = CommonLib.ProcessorTrueValue;
		request.businessRules.ignoreCVResult = CommonLib.ProcessorFalseValue;

		// there are two items in this sample
		request.item = new Item[1];

		Item item = new Item();
		item.id = _productNumber;
		item.productName = _preAuthorizeOperationName;
		item.unitPrice = _preAuthorizeValue;
		request.item[0] = item;

		CommonLib.RequestResult result = ProcessRequest(request, referenceCode);

		return CommonLib.RequestResult.GetStringArrayResult(result);
	}

	[WebMethod]
	public string[] ReverseAuthorization(string referenceCode,
		string authorizationRequestID, string currency) {

		if (referenceCode.Length == 0 || authorizationRequestID.Length == 0 || currency.Length == 0) {
			CommonLib.RequestResult res = new CommonLib.RequestResult();
			res.ResultString = "Reference Code, Authorization RequestID, AuthorizationToken and currency can not be empty!";
			res.RequestID = referenceCode;
			return CommonLib.RequestResult.GetStringArrayResult(res);
		}

		RequestMessage request = new RequestMessage();

		request.merchantReferenceCode = referenceCode.Trim();

		request.ccAuthReversalService = new CCAuthReversalService();
		request.ccAuthReversalService.authRequestID = authorizationRequestID;
		request.ccAuthReversalService.run = CommonLib.ProcessorTrueValue;

		request.businessRules = new BusinessRules();
		request.businessRules.ignoreAVSResult = CommonLib.ProcessorTrueValue;
		request.businessRules.ignoreCVResult = CommonLib.ProcessorFalseValue;

		PurchaseTotals purchaseTotals = new PurchaseTotals();
		purchaseTotals.currency = currency;
		request.purchaseTotals = purchaseTotals;

		request.item = new Item[1];
		Item item = new Item();
		item.id = _productNumber;
		item.productName = _preAuthorizeOperationName;
		item.unitPrice = _preAuthorizeValue;
		request.item[0] = item;

		CommonLib.RequestResult result = ProcessRequest(request, referenceCode);

		return CommonLib.RequestResult.GetStringArrayResult(result);
	}

	[WebMethod]
	public string[] UpdateProfileInformation(string subscriptionID, string referenceCode,
		string firstName, string lastName, string companyName,
		string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email) {

		RequestMessage request = new RequestMessage();
		// add required fields
		// Mark the request as profile creator
		if (referenceCode.Trim().Length > 0)
			request.merchantReferenceCode = referenceCode;
		else
			return CommonLib.RequestResult.GetStringArrayResult("-1", "Profile reference code can't be empty", referenceCode, referenceCode, subscriptionID);

		request.paySubscriptionUpdateService = new PaySubscriptionUpdateService();
		request.paySubscriptionUpdateService.run = CommonLib.ProcessorTrueValue;

		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.subscriptionID = subscriptionID;

		InitRequestCommon(request, referenceCode, firstName, lastName, companyName, address1, address2, city, state, zipCode, country, phoneNumber, email, string.Empty);

		CommonLib.RequestResult result = ProcessRequest(request, referenceCode);

		return CommonLib.RequestResult.GetStringArrayResult(result);
	}

	[WebMethod]
	public string[] UpdateCreditCardInformation(string subscriptionID, string referenceCode,
		string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear) {

		RequestMessage request = new RequestMessage();

		request.paySubscriptionUpdateService = new PaySubscriptionUpdateService();
		request.paySubscriptionUpdateService.run = CommonLib.ProcessorTrueValue;

		request.businessRules = new BusinessRules();
		request.businessRules.ignoreAVSResult = CommonLib.ProcessorTrueValue;
		request.businessRules.ignoreCVResult = CommonLib.ProcessorFalseValue;

		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.subscriptionID = subscriptionID;

		request.merchantReferenceCode = referenceCode;

		if (expMonth.Trim().Length > 0 && expYear.Trim().Length > 0) {
			Card card = new Card();
			if (ccardNumber.Trim().Length > 0)
				card.accountNumber = ccardNumber.Trim();
			card.expirationMonth = expMonth.Trim();
			card.expirationYear = expYear.Trim();
			if (cardType.Trim().Length > 0)
				card.cardType = cardType.Trim();
			if (cvNumber.Trim().Length > 0) {
				card.cvNumber = cvNumber.Trim();
				card.cvIndicator = "1";
			}

			request.card = card;
			CommonLib.RequestResult result = ProcessRequest(request, referenceCode);

			return CommonLib.RequestResult.GetStringArrayResult(result);
		} else
			return CommonLib.RequestResult.GetStringArrayResult("-1", "CCardNumber, CCardType and Experation Date can not be ampty", referenceCode, referenceCode, subscriptionID);
	}

	[WebMethod]
	public string[] CancelProfile(string subscriptionID, string profileReferenceCode) {
		RequestMessage request = new RequestMessage();

		if (subscriptionID.Length == 0 || profileReferenceCode.Length == 0) {
			CommonLib.RequestResult res = new CommonLib.RequestResult();
			res.ResultString = "Profile Reference Code and Subscripton ID can not be empty!";
			res.RequestID = profileReferenceCode;
			return CommonLib.RequestResult.GetStringArrayResult(res);
		}

		request.paySubscriptionDeleteService = new PaySubscriptionDeleteService();
		request.paySubscriptionDeleteService.run = CommonLib.ProcessorTrueValue;
		request.merchantReferenceCode = profileReferenceCode;
		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.subscriptionID = subscriptionID;

		CommonLib.RequestResult result = ProcessRequest(request, string.Empty);

		return CommonLib.RequestResult.GetStringArrayResult(result);
	}

	[WebMethod]
	public string[] ChargeMoney(string referenceCode,
		string suscriptionID,
		string serviceName, string value) {

		if (referenceCode.Length == 0 || suscriptionID.Length == 0 || value.Length == 0) {
			CommonLib.RequestResult res = new CommonLib.RequestResult();
			res.ResultString = "Reference Code, Subscripton ID and Value can not be empty!";
			res.RequestID = referenceCode;
			return CommonLib.RequestResult.GetStringArrayResult(res);
		}

		RequestMessage request = new RequestMessage();

		request.merchantReferenceCode = referenceCode.Trim();

		request.ccAuthService = new CCAuthService();
		request.ccAuthService.run = CommonLib.ProcessorTrueValue;
		request.ccCaptureService = new CCCaptureService();
		request.ccCaptureService.run = CommonLib.ProcessorTrueValue;

		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.subscriptionID = suscriptionID.Trim();

		request.businessRules = new BusinessRules();
		request.businessRules.ignoreAVSResult = CommonLib.ProcessorTrueValue;
		request.businessRules.ignoreCVResult = CommonLib.ProcessorFalseValue;

		// there are two items in this sample
		request.item = new Item[1];

		Item item = new Item();
		item.id = _productNumber;
		item.productName = serviceName;
		item.unitPrice = value.Trim();
		request.item[0] = item;

		CommonLib.RequestResult result = ProcessRequest(request, referenceCode);

		return CommonLib.RequestResult.GetStringArrayResult(result);
	}
	#endregion

	#region Electronic Debit Check
	[WebMethod]
	public string[] CreateDebitElectronicCheckProfile(string referenceCode,
		string firstName, string lastName, string companyName, string dateOfBirth,
		string address1, string address2, string city, string state, string zipCode, string country,
		string phoneNumber, string email,
		string accountNumber, string accountType, string bankTransitNumber, string currency) {

		string validation = ValidateCreateElectronicCheckProfile(referenceCode,
			firstName, lastName, companyName, dateOfBirth,
			address1, address2, city, state, zipCode, country, phoneNumber, email,
			accountNumber, accountType, bankTransitNumber, currency);
		if (validation.Length > 0) {
			CommonLib.RequestResult res = new CommonLib.RequestResult();
			res.ResultString = validation;
			res.RequestID = referenceCode;
			return CommonLib.RequestResult.GetStringArrayResult(res);
		}

		RequestMessage request = new RequestMessage();

		request.paySubscriptionCreateService = new PaySubscriptionCreateService();
		request.paySubscriptionCreateService.run = CommonLib.ProcessorTrueValue;

		InitRequestCommon(request, referenceCode, firstName, lastName, companyName, address1, address2, city, state, zipCode, country, phoneNumber, email, currency);
		if (accountType.Equals("X"))
			request.billTo.dateOfBirth = "********";

		request.check = new Check();
		request.check.accountNumber = accountNumber.Trim();
		request.check.accountType = accountType.Trim().ToUpper();
		request.check.bankTransitNumber = bankTransitNumber.Trim();

		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.automaticRenew = CommonLib.ProcessorFalseValue;
		request.recurringSubscriptionInfo.amount = "0.00";
		request.recurringSubscriptionInfo.frequency = "on-demand";

		request.subscription = new Subscription();
		request.subscription.paymentMethod = "check";

		CommonLib.RequestResult result = ProcessRequest(request, referenceCode);
		return CommonLib.RequestResult.GetStringArrayResult(result);
	}

	[WebMethod]
	public string[] UpdateDebitElectronicCheckProfile(string subscriptionID, string referenceCode,
		string firstName, string lastName, string companyName, string dateOfBirth,
		string address1, string address2, string city, string state, string zipCode, string country,
		string phoneNumber, string email,
		string accountNumber, string accountType, string bankTransitNumber, string currency) {

		RequestMessage request = new RequestMessage();

		request.paySubscriptionUpdateService = new PaySubscriptionUpdateService();
		request.paySubscriptionUpdateService.run = CommonLib.ProcessorTrueValue;

		InitRequestCommon(request, referenceCode, firstName, lastName, companyName, address1, address2, city, state, zipCode, country, phoneNumber, email, currency);
		if (accountType.Equals("X"))
			request.billTo.dateOfBirth = "********";

		if (accountNumber.Trim().Length > 0 && accountType.Trim().Length > 0 && bankTransitNumber.Trim().Length > 0) {
			request.check = new Check();
			if (accountNumber.Trim().Length > 0)
				request.check.accountNumber = accountNumber.Trim();
			if (accountType.Trim().Length > 0)
				request.check.accountType = accountType.Trim().ToUpper();
			if (bankTransitNumber.Trim().Length > 0)
				request.check.bankTransitNumber = bankTransitNumber.Trim();
		}

		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.subscriptionID = subscriptionID;

		request.subscription = new Subscription();
		request.subscription.paymentMethod = "check";

		CommonLib.RequestResult result = ProcessRequest(request, referenceCode);
		return CommonLib.RequestResult.GetStringArrayResult(result);
	}

	[WebMethod]
	public string[] CreateDebitElectronicCheckTransaction(string referenceCode,
		string suscriptionID, string checkNumber,
		string serviceName, string value) {

		// Check for null values first
		if (referenceCode == null || suscriptionID == null || value == null ||
		    checkNumber == null || serviceName == null) {
			CommonLib.RequestResult res = new CommonLib.RequestResult();
			res.ResultString = "Reference Code, Subscription ID, Value, Check Number and Service Name cannot be null!";
			res.RequestID = referenceCode ?? "NULL_REFERENCE";
			return CommonLib.RequestResult.GetStringArrayResult(res);
		}

		if (referenceCode.Length == 0 || suscriptionID.Length == 0 || value.Length == 0) {
			CommonLib.RequestResult res = new CommonLib.RequestResult();
			res.ResultString = "Reference Code, Subscripton ID and Value can not be empty!";
			res.RequestID = referenceCode;
			return CommonLib.RequestResult.GetStringArrayResult(res);
		}

		RequestMessage request = new RequestMessage();

		request.merchantReferenceCode = referenceCode.Trim();

		request.ecDebitService = new ECDebitService();
		request.ecDebitService.run = CommonLib.ProcessorTrueValue;

		request.recurringSubscriptionInfo = new RecurringSubscriptionInfo();
		request.recurringSubscriptionInfo.subscriptionID = suscriptionID.Trim();

		// there are two items in this sample
		request.item = new Item[1];

		if (checkNumber.Trim().Length > 0) {
			request.check = new Check();
			request.check.checkNumber = checkNumber.Trim();
		}

		Item item = new Item();
		item.id = _productNumber;
		item.productName = serviceName;
		item.unitPrice = value.Trim();
		request.item[0] = item;

		CommonLib.RequestResult result = ProcessRequest(request, referenceCode);
		return CommonLib.RequestResult.GetStringArrayResult(result);
	}
	#endregion

	#region Fields Validation
	private string ValidateUpdateCreditCardProfile(string subscriptionID, string profileReferenceCode,
		string firstName, string lastName, string companyName,
		string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email,
		string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear, string currency) {
		if (subscriptionID.Trim().Length == 0)
			return "Subscription ID ca not be empty!";
		else
			return ValidateCreateCreditCardProfile(profileReferenceCode, firstName, lastName, companyName,
			address1, address2, city, state, zipCode, country, phoneNumber, email,
			ccardNumber, cvNumber, cardType, expMonth, expYear, currency);
	}

	private string ValidateCreateCreditCardProfile(string profileReferenceCode,
		string firstName, string lastName, string companyName,
		string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email,
		string ccardNumber, string cvNumber, string cardType, string expMonth, string expYear, string currency) {

		StringBuilder result = new StringBuilder();
		if (profileReferenceCode.Trim().Length == 0)
			result.Append("'Profile Reference Code' can't be empty;");
		if (firstName.Trim().Length == 0)
			result.Append("'First Name' can't be empty;");
		if (lastName.Trim().Length == 0)
			result.Append("'Last Name' can't be empty;");
		if (address1.Trim().Length == 0)
			result.Append("'Address1' can't be empty;");
		if (city.Trim().Length == 0)
			result.Append("'City' can't be empty;");
		if (state.Trim().Length == 0)
			result.Append("'State' can't be empty;");
		if (zipCode.Trim().Length == 0)
			result.Append("'Zip Code' can't be empty;");
		if (country.Trim().Length == 0)
			result.Append("'Country' can't be empty;");
		if (ccardNumber.Trim().Length == 0)
			result.Append("'Credit Card Number' can't be empty;");
		if (cardType.Trim().Length == 0)
			result.Append("'Card Type' can't be empty;");
		if (expMonth.Trim().Length == 0)
			result.Append("'Credit Card Expiration - Month' can't be empty;");
		if (expYear.Trim().Length == 0)
			result.Append("'Credit Card Expiration - Year' can't be empty;");
		if (currency.Trim().Length == 0)
			result.Append("'Currency' can't be empty;");

		if (result.Length == 0)
			return string.Empty;
		else
			return result.ToString();
	}

	private string ValidateCreateElectronicCheckProfile(string profileReferenceCode,
		string firstName, string lastName, string companyName, string dateOfBirth,
		string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email,
		string accountNumber, string accountType, string bankTransitNumber, string currency) {

		StringBuilder result = new StringBuilder();
		if (profileReferenceCode.Trim().Length == 0)
			result.Append("'Profile Reference Code' can't be empty;");
		if (firstName.Trim().Length == 0)
			result.Append("'First Name' can't be empty;");
		if (lastName.Trim().Length == 0)
			result.Append("'Last Name' can't be empty;");

		if (address1.Trim().Length == 0)
			result.Append("'Address1' can't be empty;");
		if (city.Trim().Length == 0)
			result.Append("'City' can't be empty;");
		if (state.Trim().Length == 0)
			result.Append("'State' can't be empty;");
		if (zipCode.Trim().Length == 0)
			result.Append("'Zip Code' can't be empty;");
		if (country.Trim().Length == 0)
			result.Append("'Country' can't be empty;");
		if (currency.Trim().Length == 0)
			result.Append("'Currency' can't be empty;");
		if (accountNumber.Trim().Length == 0)
			result.Append("'Account Number' can't be empty;");
		if (accountType.Trim().Length == 0)
			result.Append("'Account Type' can be 'X' for corporate, 'C' for checking or 'S' for saving only;");
		if (bankTransitNumber.Trim().Length == 0)
			result.Append("'Bank Transit Number' can't be empty;");

		if (accountType.Trim().ToUpper().Equals("X")) {
			if (companyName.Trim().Length == 0)
				result.Append("'Company Name' can't be empty for corporate electronic check;");
		} else {
			if (dateOfBirth.Trim().Length == 0)
				result.Append("'Date Of Birth' can't be empty for personal electronic check;");
		}

		if (result.Length == 0)
			return string.Empty;
		else
			return result.ToString();
	}

	private string ValidateUpdateElectronicCheckProfile(string subscriptionID, string profileReferenceCode,
		string firstName, string lastName, string companyName, string dateOfBirth,
		string address1, string address2, string city, string state, string zipCode, string country, string phoneNumber, string email,
		string accountNumber, string accountType, string bankTransitNumber, string currency) {
		if (subscriptionID.Trim().Length == 0)
			return "Subscription ID ca not be empty!";
		else
			return ValidateCreateElectronicCheckProfile(profileReferenceCode, firstName, lastName, companyName, dateOfBirth,
				address1, address2, city, state, zipCode, country, phoneNumber, email,
				accountNumber, accountType, bankTransitNumber, currency);
	}
	#endregion

	#region Common Request Creation
	private CommonLib.RequestResult ProcessRequest(RequestMessage request, string referenceCode) {
		CommonLib.RequestResult result = new CommonLib.RequestResult();
		ReplyMessage reply = null;
		try {
			reply = SoapClient.RunTransaction(request);
			FillOrderState(reply, result);
			ProcessReply(reply, result);
		} catch (CryptographicException crptex) {
			FillOrderState(reply, result);
			HandleCryptographicException(crptex, result);
		} catch (SoapHeaderException she) {
			FillOrderState(reply, result);
			HandleSoapHeaderException(she, result);
		} catch (SoapException se) {
			FillOrderState(reply, result);
			HandleSoapException(se, result);
		} catch (WebException we) {
			FillOrderState(reply, result);
			HandleWebException(we, result, referenceCode);
		}
		return result;
	}

	private ReplyMessage ProcessRequestReplyResponce(RequestMessage request, string referenceCode) {
		CommonLib.RequestResult result = new CommonLib.RequestResult();
		ReplyMessage reply = null;
		try {
			reply = SoapClient.RunTransaction(request);
			FillOrderState(reply, result);
			ProcessReply(reply, result);
		} catch (CryptographicException crptex) {
			FillOrderState(reply, result);
			HandleCryptographicException(crptex, result);
		} catch (SoapHeaderException she) {
			FillOrderState(reply, result);
			HandleSoapHeaderException(she, result);
		} catch (SoapException se) {
			FillOrderState(reply, result);
			HandleSoapException(se, result);
		} catch (WebException we) {
			FillOrderState(reply, result);
			HandleWebException(we, result, referenceCode);
		}
		return reply;
	}

	private void InitRequestCommon(RequestMessage request, string referenceCode,
		string firstName, string lastName, string companyName,
		string address1, string address2, string city, string state, string zipCode, string country,
		string phoneNumber, string email, string currency) {

		request.merchantReferenceCode = referenceCode;

		// Set Cutomer info
		// address
		bool isRequired = false;
		BillTo billTo = new BillTo();
		if (firstName.Trim().Length > 0) {
			billTo.firstName = firstName.Trim();
			isRequired = true;
		}
		if (lastName.Trim().Length > 0) {
			billTo.lastName = lastName.Trim();
			isRequired = true;
		}
		if (companyName.Trim().Length > 0) {
			billTo.company = companyName;
			isRequired = true;
		}
		if (address1.Trim().Length > 0) {
			billTo.street1 = address1.Trim();
			isRequired = true;
		}
		if (address2.Trim().Length > 0) {
			billTo.street2 = address2.Trim();
			isRequired = true;
		}
		if (city.Trim().Length > 0) {
			billTo.city = city.Trim();
			isRequired = true;
		}
		if (state.Trim().Length > 0) {
			billTo.state = state.Trim();
			isRequired = true;
		}
		if (zipCode.Trim().Length > 0) {
			billTo.postalCode = zipCode.Trim();
			isRequired = true;
		}
		if (country.Trim().Length > 0) {
			billTo.country = country.Trim();
			isRequired = true;
		}
		if (phoneNumber.Trim().Length > 0) {
			billTo.phoneNumber = phoneNumber.Trim();
			isRequired = true;
		}
		if (email.Trim().Length > 0) {
			billTo.email = email.Trim();
			isRequired = true;
		}
		if (isRequired)
			request.billTo = billTo;

		if (currency.Trim().Length > 0) {
			PurchaseTotals purchaseTotals = new PurchaseTotals();
			purchaseTotals.currency = currency;
			request.purchaseTotals = purchaseTotals;
		}
	}
	#endregion

	#region Private Reply Analize helpers
	private void FillOrderState(ReplyMessage reply, CommonLib.RequestResult result) {
		if (reply != null) {
			result.ResultCode = reply.reasonCode;
			if (reply.requestID != null)
				result.RequestID = reply.requestID;
			if (reply.requestToken != null)
				result.RequestToken = reply.requestToken;
			if (reply.paySubscriptionCreateReply != null)
				result.SubscriptionID = reply.paySubscriptionCreateReply.subscriptionID;

		}
	}
	private static void ProcessReply(ReplyMessage reply, CommonLib.RequestResult result) {
		string template = GetTemplate(reply.decision.ToUpper());
		string content = GetContent(reply);
		result.ResultString = string.Format(template, content);
	}
	private static string GetTemplate(string decision) {
		if ("ACCEPT".Equals(decision)) {
			return ("The transaction succeeded. {0}");
		}

		if ("REJECT".Equals(decision)) {
			return "Your order was not approved. {0}";
		}

		// ERROR
		return "Your order could not be completed at this time. {0} ";
	}
	private static string GetContent(ReplyMessage reply) {
		int reasonCode = int.Parse(reply.reasonCode);
		switch (reasonCode) {
			// Success
			case 100:
				return string.Empty;
			// Missing field(s)
			case 101:
				return (
					"The following required field(s) are missing: " +
					EnumerateValues(reply.missingField));

			// Invalid field(s)
			case 102:
				return (
					"The following field(s) are invalid: " +
					EnumerateValues(reply.invalidField));

			// Insufficient funds
			case 204:
				return (
					"Insufficient funds in the account.  Please use a " +
					"different card or select another form of payment.");
			default:
				// For all other reason codes, return an empty string,
				// in which case, the template will be displayed with no
				// specific content.
				return (string.Empty);
		}
	}

	#region Error handlers
	private static void HandleCryptographicException(CryptographicException crptex, CommonLib.RequestResult result) {
		string template = GetTemplate("ERROR");
		string content = string.Format("Failed to sign the SOAP request with error message '{0}'. The link might help: '{1}'.",
			crptex.Message, crptex.HelpLink);

		result.ResultString = string.Format(template, content);
	}
	private static void HandleSoapHeaderException(SoapHeaderException she, CommonLib.RequestResult result) {
		string template = GetTemplate("ERROR");
		string content = string.Format("SOAP header exception was returned with fault code " +
			"'{0}' and message '{1}'.", she.Code, she.Message);
		result.ResultString = string.Format(template, content);
	}
	private static void HandleSoapException(SoapException se, CommonLib.RequestResult result) {
		XmlNode detailNode = se.Detail;
		string detailXmlMessage = string.Empty;
		if (detailNode != null) detailXmlMessage = detailNode.InnerXml;
		string template = GetTemplate("ERROR");
		string content = string.Format("SOAP exception was returned with fault code " +
			"'{0}' and message '{1}'. Details: ", se.Code, se.Message, detailXmlMessage);
		result.ResultString = string.Format(template, content);

		if (se.Code.Namespace.Equals(SoapClient.CYBS_NAMESPACE) &&
			se.Code.Name.Equals("CriticalServerError")) {
			/* The transaction may have been completed by CyberSource.
             * If your request included a payment service, you should
             * notify the appropriate department in your company (e.g. by
             * sending an email) so that they can confirm if the request
             * did in fact complete by searching the CyberSource Support
             * Screens using the request id.
             */
			result.ResultString += " The transaction may have been completed by CyberSource though. Please confirm if the request did in fact complete by searching the CyberSource Support Screens.";
		}
	}
	private static void HandleWebException(WebException we, CommonLib.RequestResult result, string merchantReferenceCode) {
		string template = GetTemplate("ERROR");
		string content = string.Format("Failed to get a response with status '{0}' and " +
			"message '{1}'", we.Status, we.Message);
		result.ResultString = string.Format(template, content);
		if (IsCriticalError(we) && merchantReferenceCode.Length > 0) {
			/*
             * The transaction may have been completed by CyberSource.
             * If your request included a payment service, you should
             * notify the appropriate department in your company (e.g. by
             * sending an email) so that they can confirm if the request
             * did in fact complete by searching the CyberSource Support
             * Screens using the value of the merchantReferenceCode in
             * your request.
             */
			result.ResultString += " The transaction may have been completed by CyberSource though. Please confirm if the request did in fact complete by searching the CyberSource Support Screens using the merchantReferenceCode - " + merchantReferenceCode;
		}
	}
	private static string EnumerateValues(string[] array) {
		System.Text.StringBuilder sb = new System.Text.StringBuilder();
		if (array != null) {
			foreach (string val in array) {
				sb.Append(val + "<br/>");
			}
		}
		return (sb.ToString());
	}
	private static bool IsCriticalError(WebException we) {
		switch (we.Status) {
			case WebExceptionStatus.ProtocolError:
				if (we.Response != null) {
					HttpWebResponse response = (HttpWebResponse)we.Response;
					// GatewayTimeout may be returned if you are
					// connecting through a proxy server.
					return response.StatusCode == HttpStatusCode.GatewayTimeout;
				}
				// In case of ProtocolError, the Response property
				// should always be present.  In the unlikely case 
				// that it is not, we assume something went wrong
				// along the way and to be safe, treat it as a
				// critical error.
				return true;
			case WebExceptionStatus.ConnectFailure:
			case WebExceptionStatus.NameResolutionFailure:
			case WebExceptionStatus.ProxyNameResolutionFailure:
			case WebExceptionStatus.SendFailure:
				return false;
			default:
				return true;
		}
	}
	#endregion
	#endregion
}
