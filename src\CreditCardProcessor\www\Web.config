<?xml version="1.0"?>
<configuration>
  <appSettings>
    <add key="cybs.merchantID" value="testa_ebiz_1751532211"/>
    <add key="cybs.keysDirectory" value="D:\Work\legacy_services\src\CreditCardProcessor\Keys\"/>
    <add key="cybs.keyFilename" value="testa_ebiz_1751532211.p12"/>
    <add key="cybs.password" value="$U7qUIuYnEZ9JzTK" />
    <add key="cybs.sendToProduction" value="false"/>
    <!-- logging should normally be disabled in production as it would  -->
    <!-- slow down the processing.  Enable it only when troubleshooting -->
    <!-- an issue.                                                      -->
    <add key="cybs.enableLog" value="false"/>
    <add key="cybs.logDirectory" value="your_log_dir(baseDir\simapi-c-n.n.n\logs)"/>
    <!-- Please refer to the Connection Limit section in the README for -->
    <!-- details on this setting and alternate ways to set the          -->
    <!-- connection limit.  When not specified or is set to -1, the     -->
    <!-- client will implicitly use the connection limit currently in   -->
    <!-- force, which would be 2 if none of the alternate methods are   -->
    <!-- used.                                                          -->
    <add key="cybs.connectionLimit" value="-1"/>
    <!-- DO NOT INCLUDE THIS PROPERTY IN YOUR OWN APPLICATIONS! -->
    <add key="cybs.demo" value="true"/>
  </appSettings>
  <connectionStrings/>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8.1" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.8.1"/>
    <authentication mode="Windows"/>
    <webServices>
      <protocols>
        <remove name="HttpSoap12"/>
      </protocols>
    </webServices>
    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID"/>
  </system.web>
</configuration>